package com.melur.kotatinggi.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.adapter.ItineraryDayAdapter;
import com.melur.kotatinggi.data.DatabaseHandler;
import com.melur.kotatinggi.model.Itinerary;
import com.melur.kotatinggi.model.ItineraryDay;
import com.melur.kotatinggi.model.ItineraryItem;
import com.melur.kotatinggi.utils.Tools;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ActivityItineraryTracker extends AppCompatActivity {

    private TextView tvItineraryName, tvDateRange, tvProgress, tvStats;
    private ProgressBar progressBar;
    private RecyclerView recyclerView;
    
    private ItineraryDayAdapter adapter;
    private DatabaseHandler db;
    private Itinerary itinerary;
    private List<ItineraryDay> days = new ArrayList<>();
    
    private int itineraryId;
    private boolean trackingMode = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_itinerary_tracker);
        
        // Get intent data
        itineraryId = getIntent().getIntExtra("itinerary_id", -1);
        trackingMode = getIntent().getBooleanExtra("tracking_mode", false);
        
        if (itineraryId == -1) {
            finish();
            return;
        }
        
        initToolbar();
        initComponents();
        setupRecyclerView();
        loadItinerary();
    }

    private void initToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setDisplayShowTitleEnabled(true);
            actionBar.setTitle(trackingMode ? "Track Progress" : "Itinerary Details");
        }
        Tools.systemBarLolipop(this);
    }

    private void initComponents() {
        db = new DatabaseHandler(this);
        
        tvItineraryName = findViewById(R.id.tvItineraryName);
        tvDateRange = findViewById(R.id.tvDateRange);
        tvProgress = findViewById(R.id.tvProgress);
        tvStats = findViewById(R.id.tvStats);
        progressBar = findViewById(R.id.progressBar);
        recyclerView = findViewById(R.id.recyclerView);
    }

    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setHasFixedSize(true);
        
        adapter = new ItineraryDayAdapter(this, days, trackingMode);
        adapter.setOnItemActionListener(new ItineraryDayAdapter.OnItemActionListener() {
            @Override
            public void onItemCompleted(ItineraryItem item, boolean completed) {
                db.markItineraryItemCompleted(item.item_id, completed);
                loadItinerary(); // Refresh data
            }

            @Override
            public void onItemClick(ItineraryItem item) {
                // Open place details
                Intent intent = new Intent(ActivityItineraryTracker.this, ActivityPlaceDetail.class);
                intent.putExtra("place_id", item.place_id);
                startActivity(intent);
            }

            @Override
            public void onDayClick(ItineraryDay day) {
                if (!trackingMode) {
                    // Open day planner for editing
                    Intent intent = new Intent(ActivityItineraryTracker.this, ActivityDayPlanner.class);
                    intent.putExtra("day_id", day.day_id);
                    intent.putExtra("itinerary_id", itineraryId);
                    startActivity(intent);
                }
            }
        });
        
        recyclerView.setAdapter(adapter);
    }

    private void loadItinerary() {
        itinerary = db.getItinerary(itineraryId);
        if (itinerary == null) {
            finish();
            return;
        }
        
        // Load days with items
        days.clear();
        days.addAll(db.getItineraryDays(itineraryId));
        adapter.notifyDataSetChanged();
        
        updateUI();
    }

    private void updateUI() {
        tvItineraryName.setText(itinerary.name);
        
        // Format date range
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        String dateRange = dateFormat.format(new Date(itinerary.start_date));
        if (itinerary.start_date != itinerary.end_date) {
            dateRange += " - " + dateFormat.format(new Date(itinerary.end_date));
        }
        tvDateRange.setText(dateRange);
        
        // Update progress
        float progress = itinerary.getCompletionPercentage();
        progressBar.setProgress((int) progress);
        tvProgress.setText(String.format(Locale.getDefault(), "%.0f%% Complete", progress));
        
        // Update stats
        String statsText = String.format(Locale.getDefault(), 
                "%d days • %d places • %d completed",
                itinerary.total_days,
                itinerary.total_places,
                itinerary.completed_places);
        tvStats.setText(statsText);
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadItinerary(); // Refresh when returning from other activities
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_itinerary_tracker, menu);
        
        // Hide edit option in tracking mode
        MenuItem editItem = menu.findItem(R.id.action_edit);
        if (editItem != null) {
            editItem.setVisible(!trackingMode);
        }
        
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == android.R.id.home) {
            finish();
            return true;
        } else if (itemId == R.id.action_edit) {
            // Edit itinerary
            Intent intent = new Intent(this, ActivityItineraryCreator.class);
            intent.putExtra("itinerary_id", itineraryId);
            intent.putExtra("edit_mode", true);
            startActivity(intent);
            return true;
        } else if (itemId == R.id.action_share) {
            shareItinerary();
            return true;
        } else if (itemId == R.id.action_mark_complete) {
            markItineraryComplete();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }

    private void shareItinerary() {
        StringBuilder shareText = new StringBuilder();
        shareText.append("My Itinerary: ").append(itinerary.name).append("\\n\\n");
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        shareText.append("Dates: ").append(dateFormat.format(new Date(itinerary.start_date)));
        if (itinerary.start_date != itinerary.end_date) {
            shareText.append(" - ").append(dateFormat.format(new Date(itinerary.end_date)));
        }
        shareText.append("\\n\\n");
        
        if (!itinerary.description.isEmpty()) {
            shareText.append("Description: ").append(itinerary.description).append("\\n\\n");
        }
        
        shareText.append("Places to visit:\\n");
        for (ItineraryDay day : days) {
            shareText.append("\\nDay ").append(day.day_number).append(": ").append(day.title).append("\\n");
            for (ItineraryItem item : day.items) {
                if (item.place != null) {
                    shareText.append("• ").append(item.place.name);
                    if (!item.visit_time.isEmpty()) {
                        shareText.append(" (").append(item.visit_time).append(")");
                    }
                    shareText.append("\\n");
                }
            }
        }
        
        shareText.append("\\nShared from Kota Tinggi Tourism App");
        
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, shareText.toString());
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, "My Travel Itinerary: " + itinerary.name);
        startActivity(Intent.createChooser(shareIntent, "Share Itinerary"));
    }

    private void markItineraryComplete() {
        itinerary.is_completed = true;
        db.updateItinerary(itinerary);
        loadItinerary();
    }
}