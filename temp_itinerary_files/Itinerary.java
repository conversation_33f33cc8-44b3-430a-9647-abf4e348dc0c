package com.melur.kotatinggi.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Itinerary implements Serializable {
    public int itinerary_id;
    public String name = "";
    public String description = "";
    public long start_date;
    public long end_date;
    public long created_date;
    public boolean is_completed = false;
    public String cover_image = "";
    
    // Calculated fields
    public int total_places = 0;
    public int completed_places = 0;
    public int total_days = 0;
    
    // Related data
    public List<ItineraryDay> days = new ArrayList<>();
    
    public Itinerary() {
        this.created_date = System.currentTimeMillis();
    }
    
    public Itinerary(String name, String description, long start_date, long end_date) {
        this();
        this.name = name;
        this.description = description;
        this.start_date = start_date;
        this.end_date = end_date;
        this.total_days = calculateTotalDays();
    }
    
    public int calculateTotalDays() {
        if (start_date == 0 || end_date == 0) return 0;
        return (int) ((end_date - start_date) / (1000 * 60 * 60 * 24)) + 1;
    }
    
    public float getCompletionPercentage() {
        if (total_places == 0) return 0f;
        return (float) completed_places / total_places * 100f;
    }
    
    public boolean isActive() {
        long currentTime = System.currentTimeMillis();
        return currentTime >= start_date && currentTime <= end_date;
    }
    
    public boolean isUpcoming() {
        return System.currentTimeMillis() < start_date;
    }
    
    public boolean isPast() {
        return System.currentTimeMillis() > end_date;
    }
    
    public String getStatusText() {
        if (is_completed) return "Completed";
        if (isActive()) return "Active";
        if (isUpcoming()) return "Upcoming";
        if (isPast()) return "Past";
        return "Draft";
    }
}