package com.melur.kotatinggi.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.adapter.ItineraryAdapter;
import com.melur.kotatinggi.data.DatabaseHandler;
import com.melur.kotatinggi.model.Itinerary;
import com.melur.kotatinggi.utils.Tools;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class ActivityItineraryHub extends AppCompatActivity {

    private RecyclerView recyclerView;
    private ItineraryAdapter adapter;
    private FloatingActionButton fabCreateItinerary;
    private TextView tvEmptyState;
    private TextView tvStats;
    
    private DatabaseHandler db;
    private List<Itinerary> itineraries = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_itinerary_hub);
        
        initToolbar();
        initComponents();
        setupRecyclerView();
        loadItineraries();
    }

    private void initToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setDisplayShowTitleEnabled(true);
            actionBar.setTitle("My Itineraries");
        }
        Tools.systemBarLolipop(this);
    }

    private void initComponents() {
        db = new DatabaseHandler(this);
        recyclerView = findViewById(R.id.recyclerView);
        fabCreateItinerary = findViewById(R.id.fabCreateItinerary);
        tvEmptyState = findViewById(R.id.tvEmptyState);
        tvStats = findViewById(R.id.tvStats);

        fabCreateItinerary.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ActivityItineraryHub.this, ActivityItineraryCreator.class);
                startActivity(intent);
            }
        });
    }

    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new GridLayoutManager(this, 1));
        recyclerView.setHasFixedSize(true);
        
        adapter = new ItineraryAdapter(this, itineraries);
        adapter.setOnItemClickListener(new ItineraryAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, Itinerary itinerary, int position) {
                openItineraryDetails(itinerary);
            }

            @Override
            public void onTrackClick(Itinerary itinerary) {
                openItineraryTracker(itinerary);
            }

            @Override
            public void onEditClick(Itinerary itinerary) {
                editItinerary(itinerary);
            }
        });
        
        recyclerView.setAdapter(adapter);
    }

    private void loadItineraries() {
        itineraries.clear();
        itineraries.addAll(db.getAllItineraries());
        adapter.notifyDataSetChanged();
        
        updateUI();
        updateStats();
    }

    private void updateUI() {
        if (itineraries.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            tvEmptyState.setVisibility(View.VISIBLE);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            tvEmptyState.setVisibility(View.GONE);
        }
    }

    private void updateStats() {
        int activeItineraries = 0;
        int completedPlaces = 0;
        int totalPlaces = 0;
        
        for (Itinerary itinerary : itineraries) {
            if (itinerary.isActive() || itinerary.isUpcoming()) {
                activeItineraries++;
            }
            completedPlaces += itinerary.completed_places;
            totalPlaces += itinerary.total_places;
        }
        
        String statsText;
        if (totalPlaces > 0) {
            statsText = String.format("%d Active • %d/%d Places Visited", 
                    activeItineraries, completedPlaces, totalPlaces);
        } else {
            statsText = "No trips planned yet";
        }
        tvStats.setText(statsText);
    }

    private void openItineraryDetails(Itinerary itinerary) {
        Intent intent = new Intent(this, ActivityItineraryTracker.class);
        intent.putExtra("itinerary_id", itinerary.itinerary_id);
        startActivity(intent);
    }

    private void openItineraryTracker(Itinerary itinerary) {
        Intent intent = new Intent(this, ActivityItineraryTracker.class);
        intent.putExtra("itinerary_id", itinerary.itinerary_id);
        intent.putExtra("tracking_mode", true);
        startActivity(intent);
    }

    private void editItinerary(Itinerary itinerary) {
        Intent intent = new Intent(this, ActivityItineraryCreator.class);
        intent.putExtra("itinerary_id", itinerary.itinerary_id);
        intent.putExtra("edit_mode", true);
        startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadItineraries(); // Refresh data when returning to this activity
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_itinerary_hub, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == android.R.id.home) {
            finish();
            return true;
        } else if (itemId == R.id.action_templates) {
            // Open templates activity (to be implemented)
            return true;
        } else if (itemId == R.id.action_settings) {
            // Open itinerary settings
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
}