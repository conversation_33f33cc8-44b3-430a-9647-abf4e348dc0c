<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header with Image -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="200dp">

            <ImageView
                android:id="@+id/ivCover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/loading_placeholder" />

            <!-- Gradient Overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_overlay" />

            <!-- Status Badge -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_margin="12dp"
                android:background="@drawable/round_bg"
                android:paddingLeft="12dp"
                android:paddingTop="6dp"
                android:paddingRight="12dp"
                android:paddingBottom="6dp"
                android:text="Active"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

            <!-- Title and Date at Bottom -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="Kota Tinggi Heritage Tour"
                    android:textColor="@android:color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDateRange"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Dec 15, 2024 - Dec 17, 2024"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

            </LinearLayout>

        </RelativeLayout>

        <!-- Content Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Description -->
            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Explore the historical and cultural attractions of Kota Tinggi district"
                android:textColor="@color/grey_700"
                android:textSize="14sp" />

            <!-- Days and Places Info -->
            <TextView
                android:id="@+id/tvDaysPlaces"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="3 days • 12 places"
                android:textColor="@color/grey_600"
                android:textSize="12sp" />

            <!-- Progress Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Progress"
                        android:textColor="@color/grey_700"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvProgress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="25% Complete"
                        android:textColor="@color/grey_600"
                        android:textSize="12sp" />

                </LinearLayout>

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="4dp"
                    android:max="100"
                    android:progress="25"
                    android:progressTint="@color/colorPrimary" />

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="end"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnEdit"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="Edit"
                    android:textColor="@color/colorPrimary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAction"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Track Progress"
                    app:cornerRadius="20dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>