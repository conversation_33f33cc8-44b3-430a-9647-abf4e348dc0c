package com.melur.kotatinggi.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.model.Itinerary;
import com.melur.kotatinggi.utils.Tools;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.google.android.material.button.MaterialButton;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ItineraryAdapter extends RecyclerView.Adapter<ItineraryAdapter.ViewHolder> {

    private Context context;
    private List<Itinerary> itineraries;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(View view, Itinerary itinerary, int position);
        void onTrackClick(Itinerary itinerary);
        void onEditClick(Itinerary itinerary);
    }

    public ItineraryAdapter(Context context, List<Itinerary> itineraries) {
        this.context = context;
        this.itineraries = itineraries;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_itinerary, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Itinerary itinerary = itineraries.get(position);
        
        holder.tvName.setText(itinerary.name);
        holder.tvDescription.setText(itinerary.description);
        holder.tvStatus.setText(itinerary.getStatusText());
        
        // Format dates
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        String dateRange = dateFormat.format(new Date(itinerary.start_date));
        if (itinerary.start_date != itinerary.end_date) {
            dateRange += " - " + dateFormat.format(new Date(itinerary.end_date));
        }
        holder.tvDateRange.setText(dateRange);
        
        // Days and places info
        String info = String.format(Locale.getDefault(), "%d day%s • %d place%s",
                itinerary.total_days,
                itinerary.total_days == 1 ? "" : "s",
                itinerary.total_places,
                itinerary.total_places == 1 ? "" : "s");
        holder.tvDaysPlaces.setText(info);
        
        // Progress
        float progress = itinerary.getCompletionPercentage();
        holder.progressBar.setProgress((int) progress);
        holder.tvProgress.setText(String.format(Locale.getDefault(), "%.0f%% Complete", progress));
        
        // Cover image
        if (!itinerary.cover_image.isEmpty()) {
            Glide.with(context)
                    .load(itinerary.cover_image)
                    .placeholder(R.drawable.loading_placeholder)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(holder.ivCover);
        } else {
            holder.ivCover.setImageResource(R.drawable.ic_nav_tour);
        }
        
        // Status-based styling
        updateStatusStyling(holder, itinerary);
        
        // Button actions
        if (itinerary.isActive()) {
            holder.btnAction.setText("Track Progress");
            holder.btnAction.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onTrackClick(itinerary);
                }
            });
        } else if (itinerary.isUpcoming()) {
            holder.btnAction.setText("View Details");
            holder.btnAction.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(v, itinerary, position);
                }
            });
        } else if (itinerary.isPast() && !itinerary.is_completed) {
            holder.btnAction.setText("Complete");
            holder.btnAction.setOnClickListener(v -> {
                // Mark as completed logic
                if (onItemClickListener != null) {
                    onItemClickListener.onEditClick(itinerary);
                }
            });
        } else {
            holder.btnAction.setText("View");
            holder.btnAction.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(v, itinerary, position);
                }
            });
        }
        
        // Card click
        holder.cardView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(v, itinerary, position);
            }
        });
        
        // Edit button
        holder.btnEdit.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onEditClick(itinerary);
            }
        });
    }

    private void updateStatusStyling(ViewHolder holder, Itinerary itinerary) {
        int statusColor;
        int statusBgColor;
        
        if (itinerary.is_completed) {
            statusColor = Tools.getColor(context, R.color.light_green_600);
            statusBgColor = Tools.getColor(context, R.color.light_green_100);
        } else if (itinerary.isActive()) {
            statusColor = Tools.getColor(context, R.color.blue_600);
            statusBgColor = Tools.getColor(context, R.color.blue_100);
        } else if (itinerary.isUpcoming()) {
            statusColor = Tools.getColor(context, R.color.orange_600);
            statusBgColor = Tools.getColor(context, R.color.orange_100);
        } else {
            statusColor = Tools.getColor(context, R.color.grey_600);
            statusBgColor = Tools.getColor(context, R.color.grey_100);
        }
        
        holder.tvStatus.setTextColor(statusColor);
        // You can set background tint if needed
    }

    @Override
    public int getItemCount() {
        return itineraries.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public CardView cardView;
        public ImageView ivCover;
        public TextView tvName;
        public TextView tvDescription;
        public TextView tvStatus;
        public TextView tvDateRange;
        public TextView tvDaysPlaces;
        public TextView tvProgress;
        public ProgressBar progressBar;
        public MaterialButton btnAction;
        public MaterialButton btnEdit;

        public ViewHolder(View view) {
            super(view);
            cardView = view.findViewById(R.id.cardView);
            ivCover = view.findViewById(R.id.ivCover);
            tvName = view.findViewById(R.id.tvName);
            tvDescription = view.findViewById(R.id.tvDescription);
            tvStatus = view.findViewById(R.id.tvStatus);
            tvDateRange = view.findViewById(R.id.tvDateRange);
            tvDaysPlaces = view.findViewById(R.id.tvDaysPlaces);
            tvProgress = view.findViewById(R.id.tvProgress);
            progressBar = view.findViewById(R.id.progressBar);
            btnAction = view.findViewById(R.id.btnAction);
            btnEdit = view.findViewById(R.id.btnEdit);
        }
    }
}