<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grey_10"
    tools:context=".activity.ActivityItineraryCreator">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <include layout="@layout/toolbar" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Basic Information"
                        android:textColor="@color/grey_800"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- Itinerary Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Itinerary Name"
                        app:boxStrokeColor="@color/colorPrimary"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Description (Optional)"
                        app:boxStrokeColor="@color/colorPrimary"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine|textCapSentences"
                            android:maxLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Date Selection Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="Travel Dates"
                        android:textColor="@color/grey_800"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- Start Date -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Start Date"
                            android:textColor="@color/grey_700"
                            android:textSize="16sp" />

                        <Button
                            android:id="@+id/btnStartDate"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="Select Date"
                            android:textColor="@color/colorPrimary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvStartDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/round_shape"
                        android:padding="12dp"
                        android:text="Dec 15, 2024"
                        android:textColor="@color/grey_800"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- End Date -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="End Date"
                            android:textColor="@color/grey_700"
                            android:textSize="16sp" />

                        <Button
                            android:id="@+id/btnEndDate"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="Select Date"
                            android:textColor="@color/colorPrimary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvEndDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/round_shape"
                        android:padding="12dp"
                        android:text="Dec 17, 2024"
                        android:textColor="@color/grey_800"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Duration Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/blue_100"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_date"
                            android:tint="@color/blue_600" />

                        <TextView
                            android:id="@+id/tvDurationInfo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3 days trip"
                            android:textColor="@color/blue_600"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSaveItinerary"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="16dp"
                android:text="Create Itinerary"
                android:textSize="16sp"
                app:cornerRadius="28dp"
                app:icon="@drawable/ic_nav_tour" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>