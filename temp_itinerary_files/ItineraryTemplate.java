package com.melur.kotatinggi.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ItineraryTemplate implements Serializable {
    public int template_id;
    public String name = "";
    public String description = "";
    public int duration_days = 1;
    public String category = ""; // "cultural", "nature", "food", "adventure", etc.
    public String places_json = ""; // JSON array of place IDs and suggested times
    public String cover_image = "";
    public int popularity_score = 0; // How many times this template was used
    public long created_date;
    public boolean is_featured = false;
    
    // Parsed data (not stored in DB)
    public List<TemplatePlace> template_places = new ArrayList<>();
    
    public ItineraryTemplate() {
        this.created_date = System.currentTimeMillis();
    }
    
    public ItineraryTemplate(String name, String description, int duration_days, String category) {
        this();
        this.name = name;
        this.description = description;
        this.duration_days = duration_days;
        this.category = category;
    }
    
    public String getDurationText() {
        if (duration_days == 1) {
            return "1 Day";
        } else {
            return duration_days + " Days";
        }
    }
    
    public String getCategoryDisplay() {
        if (category.isEmpty()) return "General";
        return category.substring(0, 1).toUpperCase() + category.substring(1);
    }
    
    public boolean isPopular() {
        return popularity_score > 10; // Threshold for popular templates
    }
    
    // Inner class for template place structure
    public static class TemplatePlace implements Serializable {
        public int place_id;
        public int day_number;
        public String suggested_time;
        public int suggested_duration;
        public String notes;
        
        public TemplatePlace() {
        }
        
        public TemplatePlace(int place_id, int day_number, String suggested_time, int suggested_duration) {
            this.place_id = place_id;
            this.day_number = day_number;
            this.suggested_time = suggested_time;
            this.suggested_duration = suggested_duration;
        }
    }
}