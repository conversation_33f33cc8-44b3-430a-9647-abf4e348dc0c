<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grey_10"
    tools:context=".activity.ActivityItineraryHub">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <include layout="@layout/toolbar" />

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Stats Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvStats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="0 Active • 0/0 Places Visited"
                android:textColor="@color/grey_700"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Main Content -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- RecyclerView for itineraries -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:padding="8dp"
                android:scrollbars="vertical" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/tvEmptyState"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="24dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_nav_tour"
                    android:tint="@color/grey_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="No Itineraries Yet"
                    android:textColor="@color/grey_700"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:gravity="center"
                    android:text="Create your first travel itinerary and start exploring Kota Tinggi!"
                    android:textColor="@color/grey_500"
                    android:textSize="16sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnCreateFirst"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create Itinerary"
                    app:icon="@drawable/ic_nav_tour" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabCreateItinerary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>