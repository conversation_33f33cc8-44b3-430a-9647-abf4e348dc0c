package com.melur.kotatinggi.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.data.Constant;
import com.melur.kotatinggi.model.ItineraryDay;
import com.melur.kotatinggi.model.ItineraryItem;
import com.melur.kotatinggi.utils.Tools;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ItineraryDayAdapter extends RecyclerView.Adapter<ItineraryDayAdapter.ViewHolder> {

    private Context context;
    private List<ItineraryDay> days;
    private boolean trackingMode;
    private OnItemActionListener onItemActionListener;

    public interface OnItemActionListener {
        void onItemCompleted(ItineraryItem item, boolean completed);
        void onItemClick(ItineraryItem item);
        void onDayClick(ItineraryDay day);
    }

    public ItineraryDayAdapter(Context context, List<ItineraryDay> days, boolean trackingMode) {
        this.context = context;
        this.days = days;
        this.trackingMode = trackingMode;
    }

    public void setOnItemActionListener(OnItemActionListener onItemActionListener) {
        this.onItemActionListener = onItemActionListener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_itinerary_day, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ItineraryDay day = days.get(position);
        
        // Day header
        holder.tvDayNumber.setText("Day " + day.day_number);
        holder.tvDayTitle.setText(day.title);
        
        // Format date
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, MMM dd", Locale.getDefault());
        holder.tvDayDate.setText(dateFormat.format(new Date(day.date)));
        
        // Progress info
        holder.tvProgress.setText(String.format(Locale.getDefault(), 
                "%d/%d places completed", day.completed_items, day.total_items));
        
        // Progress styling based on completion
        if (day.isCompleted()) {
            holder.cardView.setCardBackgroundColor(Tools.getColor(context, R.color.light_green_50));
            holder.tvProgress.setTextColor(Tools.getColor(context, R.color.light_green_600));
        } else if (day.isToday()) {
            holder.cardView.setCardBackgroundColor(Tools.getColor(context, R.color.blue_50));
            holder.tvProgress.setTextColor(Tools.getColor(context, R.color.blue_600));
        } else {
            holder.cardView.setCardBackgroundColor(Tools.getColor(context, android.R.color.white));
            holder.tvProgress.setTextColor(Tools.getColor(context, R.color.grey_600));
        }
        
        // Clear previous items
        holder.itemsContainer.removeAllViews();
        
        // Add items
        LayoutInflater inflater = LayoutInflater.from(context);
        for (ItineraryItem item : day.items) {
            View itemView = inflater.inflate(R.layout.item_itinerary_place, holder.itemsContainer, false);
            bindItemView(itemView, item);
            holder.itemsContainer.addView(itemView);
        }
        
        // Day click
        holder.cardView.setOnClickListener(v -> {
            if (onItemActionListener != null) {
                onItemActionListener.onDayClick(day);
            }
        });
    }

    private void bindItemView(View itemView, ItineraryItem item) {
        ImageView ivPlaceImage = itemView.findViewById(R.id.ivPlaceImage);
        TextView tvPlaceName = itemView.findViewById(R.id.tvPlaceName);
        TextView tvVisitTime = itemView.findViewById(R.id.tvVisitTime);
        TextView tvDuration = itemView.findViewById(R.id.tvDuration);
        CheckBox cbCompleted = itemView.findViewById(R.id.cbCompleted);
        View statusIndicator = itemView.findViewById(R.id.statusIndicator);
        
        if (item.place != null) {
            tvPlaceName.setText(item.place.name);
            
            // Load place image
            String imageUrl = Constant.getURLimgPlace(item.place.image);
            Glide.with(context)
                    .load(imageUrl)
                    .placeholder(R.drawable.loading_placeholder)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(ivPlaceImage);
        } else {
            tvPlaceName.setText("Unknown Place");
            ivPlaceImage.setImageResource(R.drawable.ic_no_item);
        }
        
        tvVisitTime.setText(item.getVisitTimeDisplay());
        tvDuration.setText(item.getDurationText());
        
        // Completion checkbox
        cbCompleted.setChecked(item.is_completed);
        cbCompleted.setVisibility(trackingMode ? View.VISIBLE : View.GONE);
        
        // Status indicator
        if (item.is_completed) {
            statusIndicator.setBackgroundColor(Tools.getColor(context, R.color.light_green_500));
        } else if (trackingMode) {
            statusIndicator.setBackgroundColor(Tools.getColor(context, R.color.orange_500));
        } else {
            statusIndicator.setBackgroundColor(Tools.getColor(context, R.color.grey_300));
        }
        
        // Click listeners
        cbCompleted.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (onItemActionListener != null) {
                onItemActionListener.onItemCompleted(item, isChecked);
            }
        });
        
        itemView.setOnClickListener(v -> {
            if (onItemActionListener != null) {
                onItemActionListener.onItemClick(item);
            }
        });
        
        // Visual feedback for completion
        float alpha = item.is_completed ? 0.7f : 1.0f;
        itemView.setAlpha(alpha);
    }

    @Override
    public int getItemCount() {
        return days.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public CardView cardView;
        public TextView tvDayNumber;
        public TextView tvDayTitle;
        public TextView tvDayDate;
        public TextView tvProgress;
        public LinearLayout itemsContainer;

        public ViewHolder(View view) {
            super(view);
            cardView = view.findViewById(R.id.cardView);
            tvDayNumber = view.findViewById(R.id.tvDayNumber);
            tvDayTitle = view.findViewById(R.id.tvDayTitle);
            tvDayDate = view.findViewById(R.id.tvDayDate);
            tvProgress = view.findViewById(R.id.tvProgress);
            itemsContainer = view.findViewById(R.id.itemsContainer);
        }
    }
}