package com.melur.kotatinggi.model;

import java.io.Serializable;

public class ItineraryItem implements Serializable {
    public int item_id;
    public int day_id;
    public int place_id;
    public int order_index;
    public String visit_time = ""; // Format: "09:00" or "Morning"
    public int duration_minutes = 60; // Default 1 hour
    public boolean is_completed = false;
    public String user_notes = "";
    public float user_rating = 0f; // 0-5 stars
    public long visit_date = 0; // Actual visit timestamp
    public long created_date;
    
    // Related data (populated by join queries)
    public Place place;
    
    public ItineraryItem() {
        this.created_date = System.currentTimeMillis();
    }
    
    public ItineraryItem(int day_id, int place_id, int order_index) {
        this();
        this.day_id = day_id;
        this.place_id = place_id;
        this.order_index = order_index;
    }
    
    public ItineraryItem(int day_id, int place_id, int order_index, String visit_time, int duration_minutes) {
        this(day_id, place_id, order_index);
        this.visit_time = visit_time;
        this.duration_minutes = duration_minutes;
    }
    
    public boolean isVisited() {
        return visit_date > 0;
    }
    
    public boolean hasRating() {
        return user_rating > 0;
    }
    
    public boolean hasNotes() {
        return !user_notes.isEmpty();
    }
    
    public String getDurationText() {
        if (duration_minutes < 60) {
            return duration_minutes + " min";
        } else {
            int hours = duration_minutes / 60;
            int minutes = duration_minutes % 60;
            if (minutes == 0) {
                return hours + " hr";
            } else {
                return hours + "h " + minutes + "m";
            }
        }
    }
    
    public String getVisitTimeDisplay() {
        if (visit_time.isEmpty()) {
            return "Anytime";
        }
        return visit_time;
    }
    
    public String getRatingStars() {
        if (user_rating == 0) return "Not rated";
        
        StringBuilder stars = new StringBuilder();
        int fullStars = (int) user_rating;
        boolean hasHalfStar = (user_rating - fullStars) >= 0.5f;
        
        for (int i = 0; i < fullStars; i++) {
            stars.append("★");
        }
        if (hasHalfStar) {
            stars.append("☆");
        }
        
        return stars.toString() + " (" + user_rating + "/5)";
    }
    
    public String getStatusText() {
        if (is_completed && isVisited()) {
            return "Completed";
        } else if (is_completed) {
            return "Marked Complete";
        } else {
            return "Pending";
        }
    }
}