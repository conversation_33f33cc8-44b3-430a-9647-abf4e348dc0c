package com.melur.kotatinggi.activity;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.data.DatabaseHandler;
import com.melur.kotatinggi.model.Itinerary;
import com.melur.kotatinggi.model.ItineraryDay;
import com.melur.kotatinggi.utils.Tools;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class ActivityItineraryCreator extends AppCompatActivity {

    private EditText etName, etDescription;
    private TextView tvStartDate, tvEndDate, tvDurationInfo;
    private Button btnStartDate, btnEndDate, btnSaveItinerary;
    
    private DatabaseHandler db;
    private Calendar startCalendar, endCalendar;
    private SimpleDateFormat dateFormat;
    
    private boolean isEditMode = false;
    private int itineraryId = -1;
    private Itinerary currentItinerary;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_itinerary_creator);
        
        initToolbar();
        initComponents();
        setupDatePickers();
        
        // Check if we're in edit mode
        if (getIntent().hasExtra("edit_mode") && getIntent().getBooleanExtra("edit_mode", false)) {
            isEditMode = true;
            itineraryId = getIntent().getIntExtra("itinerary_id", -1);
            loadExistingItinerary();
        }
    }

    private void initToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setDisplayShowTitleEnabled(true);
            actionBar.setTitle(isEditMode ? "Edit Itinerary" : "Create Itinerary");
        }
        Tools.systemBarLolipop(this);
    }

    private void initComponents() {
        db = new DatabaseHandler(this);
        
        etName = findViewById(R.id.etName);
        etDescription = findViewById(R.id.etDescription);
        tvStartDate = findViewById(R.id.tvStartDate);
        tvEndDate = findViewById(R.id.tvEndDate);
        tvDurationInfo = findViewById(R.id.tvDurationInfo);
        btnStartDate = findViewById(R.id.btnStartDate);
        btnEndDate = findViewById(R.id.btnEndDate);
        btnSaveItinerary = findViewById(R.id.btnSaveItinerary);
        
        startCalendar = Calendar.getInstance();
        endCalendar = Calendar.getInstance();
        endCalendar.add(Calendar.DAY_OF_MONTH, 1); // Default to next day
        
        dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        
        updateDateDisplays();
        updateDurationInfo();
        
        btnSaveItinerary.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveItinerary();
            }
        });
    }

    private void setupDatePickers() {
        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker(startCalendar, new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        startCalendar.set(year, month, dayOfMonth);
                        
                        // Ensure end date is not before start date
                        if (endCalendar.before(startCalendar)) {
                            endCalendar.setTimeInMillis(startCalendar.getTimeInMillis());
                            endCalendar.add(Calendar.DAY_OF_MONTH, 1);
                        }
                        
                        updateDateDisplays();
                        updateDurationInfo();
                    }
                });
            }
        });

        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker(endCalendar, new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        endCalendar.set(year, month, dayOfMonth);
                        
                        // Ensure end date is not before start date
                        if (endCalendar.before(startCalendar)) {
                            endCalendar.setTimeInMillis(startCalendar.getTimeInMillis());
                        }
                        
                        updateDateDisplays();
                        updateDurationInfo();
                    }
                });
            }
        });
    }

    private void showDatePicker(Calendar calendar, DatePickerDialog.OnDateSetListener listener) {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                listener,
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );
        
        // Set minimum date to today
        datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
        datePickerDialog.show();
    }

    private void updateDateDisplays() {
        tvStartDate.setText(dateFormat.format(startCalendar.getTime()));
        tvEndDate.setText(dateFormat.format(endCalendar.getTime()));
    }

    private void updateDurationInfo() {
        long startTime = startCalendar.getTimeInMillis();
        long endTime = endCalendar.getTimeInMillis();
        long diffDays = (endTime - startTime) / (1000 * 60 * 60 * 24) + 1;
        
        String durationText = String.format(Locale.getDefault(), 
                "%d day%s trip", diffDays, diffDays == 1 ? "" : "s");
        tvDurationInfo.setText(durationText);
    }

    private void loadExistingItinerary() {
        if (itineraryId != -1) {
            currentItinerary = db.getItinerary(itineraryId);
            if (currentItinerary != null) {
                etName.setText(currentItinerary.name);
                etDescription.setText(currentItinerary.description);
                
                startCalendar.setTimeInMillis(currentItinerary.start_date);
                endCalendar.setTimeInMillis(currentItinerary.end_date);
                
                updateDateDisplays();
                updateDurationInfo();
                
                btnSaveItinerary.setText("Update Itinerary");
            }
        }
    }

    private void saveItinerary() {
        String name = etName.getText().toString().trim();
        String description = etDescription.getText().toString().trim();
        
        if (name.isEmpty()) {
            etName.setError("Please enter itinerary name");
            etName.requestFocus();
            return;
        }
        
        long startTime = startCalendar.getTimeInMillis();
        long endTime = endCalendar.getTimeInMillis();
        
        if (isEditMode && currentItinerary != null) {
            // Update existing itinerary
            currentItinerary.name = name;
            currentItinerary.description = description;
            currentItinerary.start_date = startTime;
            currentItinerary.end_date = endTime;
            
            int result = db.updateItinerary(currentItinerary);
            if (result > 0) {
                Toast.makeText(this, "Itinerary updated successfully!", Toast.LENGTH_SHORT).show();
                finish();
            } else {
                Toast.makeText(this, "Failed to update itinerary", Toast.LENGTH_SHORT).show();
            }
        } else {
            // Create new itinerary
            Itinerary itinerary = new Itinerary(name, description, startTime, endTime);
            
            long itineraryId = db.insertItinerary(itinerary);
            if (itineraryId != -1) {
                // Create default days for the itinerary
                createDefaultDays((int) itineraryId, startTime, endTime);
                
                Toast.makeText(this, "Itinerary created successfully!", Toast.LENGTH_SHORT).show();
                finish();
            } else {
                Toast.makeText(this, "Failed to create itinerary", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void createDefaultDays(int itineraryId, long startTime, long endTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startTime);
        
        int dayNumber = 1;
        while (calendar.getTimeInMillis() <= endTime) {
            ItineraryDay day = new ItineraryDay(itineraryId, dayNumber, calendar.getTimeInMillis());
            db.insertItineraryDay(day);
            
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            dayNumber++;
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}