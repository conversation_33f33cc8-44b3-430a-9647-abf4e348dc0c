package com.melur.kotatinggi.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ItineraryDay implements Serializable {
    public int day_id;
    public int itinerary_id;
    public int day_number;
    public long date;
    public String title = "";
    public String notes = "";
    
    // Calculated fields
    public int total_items = 0;
    public int completed_items = 0;
    public int estimated_duration_minutes = 0;
    
    // Related data
    public List<ItineraryItem> items = new ArrayList<>();
    
    public ItineraryDay() {
    }
    
    public ItineraryDay(int itinerary_id, int day_number, long date) {
        this.itinerary_id = itinerary_id;
        this.day_number = day_number;
        this.date = date;
        this.title = "Day " + day_number;
    }
    
    public ItineraryDay(int itinerary_id, int day_number, long date, String title) {
        this(itinerary_id, day_number, date);
        this.title = title;
    }
    
    public float getCompletionPercentage() {
        if (total_items == 0) return 0f;
        return (float) completed_items / total_items * 100f;
    }
    
    public boolean isCompleted() {
        return total_items > 0 && completed_items == total_items;
    }
    
    public boolean isToday() {
        long today = System.currentTimeMillis();
        long dayStart = today - (today % (24 * 60 * 60 * 1000));
        long dayEnd = dayStart + (24 * 60 * 60 * 1000) - 1;
        return date >= dayStart && date <= dayEnd;
    }
    
    public boolean isPast() {
        long today = System.currentTimeMillis();
        long dayStart = today - (today % (24 * 60 * 60 * 1000));
        return date < dayStart;
    }
    
    public boolean isFuture() {
        long today = System.currentTimeMillis();
        long dayEnd = today - (today % (24 * 60 * 60 * 1000)) + (24 * 60 * 60 * 1000) - 1;
        return date > dayEnd;
    }
    
    public String getEstimatedDurationText() {
        if (estimated_duration_minutes < 60) {
            return estimated_duration_minutes + " min";
        } else {
            int hours = estimated_duration_minutes / 60;
            int minutes = estimated_duration_minutes % 60;
            if (minutes == 0) {
                return hours + " hr";
            } else {
                return hours + "h " + minutes + "m";
            }
        }
    }
}