# Screenshots Directory

This directory contains screenshots of the KotaTinggi app showcasing its main features.

## Required Screenshots

Please add the following screenshots to properly document the app:

### Core Features
- `home.png` - Main home screen showing place listings
- `details.png` - Place detail view with information and images
- `map.png` - Interactive map view with markers
- `categories.png` - Category browsing interface

### Additional Features
- `search.png` - Search functionality and results
- `favorites.png` - Favorites/bookmarks screen
- `settings.png` - App settings and configuration
- `navigation.png` - Navigation drawer or menu

## Screenshot Guidelines

### Technical Requirements
- **Resolution**: 1080x1920 (or device native resolution)
- **Format**: PNG for best quality
- **Size**: Optimize for web (< 500KB per image)
- **Orientation**: Portrait for mobile screenshots

### Content Guidelines
- Show real data when possible
- Ensure UI is in a clean, presentable state
- Include diverse content to showcase app capabilities
- Avoid personal or sensitive information
- Use high-quality, representative images

### Naming Convention
- Use descriptive, lowercase names
- Separate words with underscores or hyphens
- Include feature name in filename
- Example: `place_detail_view.png`, `map_with_markers.png`

## Usage in Documentation

Screenshots are referenced in the main README.md file and should be:
- Clear and easy to understand
- Representative of the actual app experience
- Updated when UI changes significantly
- Optimized for both light and dark themes (if applicable)

## Tools for Screenshots

### Android Studio
- Use the built-in screenshot tool in the device emulator
- Device Frame option for professional presentation

### Physical Device
- Use ADB command: `adb shell screencap -p /sdcard/screenshot.png`
- Or use device's built-in screenshot functionality

### Post-Processing
- Crop to remove unnecessary elements
- Add device frames for marketing materials
- Optimize file size without losing quality
- Consider adding annotations for complex features
