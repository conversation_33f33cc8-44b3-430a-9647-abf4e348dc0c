# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build Commands
```bash
# Build debug APK
./gradlew assembleDebug

# Build release APK  
./gradlew assembleRelease

# Build AAB for Play Store
./gradlew bundleRelease

# Clean build
./gradlew clean
```

### Installation & Testing
```bash
# Install debug APK
adb install app/build/outputs/apk/debug/app-debug.apk

# Install release APK
adb install app/build/outputs/apk/release/app-release.apk

# View logcat for debugging
adb logcat -s CITY_LOG
```

### Development Setup
- **IDE**: Android Studio Arctic Fox or later
- **Java Version**: Java 8 (sourceCompatibility/targetCompatibility)
- **Min SDK**: 24 (Android 7.0)
- **Target SDK**: 35 (Android 15)
- **Build Tools**: Gradle 8.2.2

## Architecture Overview

### Core Architecture Pattern
- **MVC Architecture** with Repository pattern
- **Data Layer**: SQLite database with custom `DatabaseHandler`
- **Network Layer**: Retrofit + Gson for API communication
- **UI Layer**: Activities + Fragments with Material Design

### Key Architectural Components

#### Data Management
- **DatabaseHandler.java**: Central SQLite database manager with CRUD operations for places, categories, news, and favorites
- **SharedPref.java**: Shared preferences wrapper for app settings
- **Constant.java**: App-wide constants and URL builders
- **AppConfig.java**: Configuration management with Firebase Remote Config support

#### Network & API
- **API.java**: Retrofit interface definitions for backend services
- **RestAdapter.java**: Retrofit client configuration and setup
- **Callbacks package**: Response models for API endpoints (places, news, device info)

#### UI Architecture
- **Activities**: Main screens (MainActivity, PlaceDetailActivity, MapsActivity, etc.)
- **Adapters**: RecyclerView adapters for different view types (grid, list, images)
- **Fragments**: Reusable UI components (FragmentCategory)

### Database Schema
- **place**: Main places table with location data, descriptions, contact info
- **category**: Place categories (tourism, food, hotels, etc.)
- **place_category**: Many-to-many relationship table
- **images**: Additional images for places
- **news_info**: News/blog articles
- **favorites_table**: User's saved places

### Configuration System
The app uses a flexible configuration system via `AppConfig.java`:
- **Remote Config**: Firebase-based configuration for dynamic updates
- **Ad Networks**: Multi-network ad support (AdMob, FAN, IronSource)
- **General Settings**: API URLs, map coordinates, feature flags
- **Notification**: OneSignal integration settings

## Key Dependencies & Integrations

### Google Services
- **Google Maps**: Places display, markers, location services
- **Play Services Location**: GPS and location tracking
- **Firebase**: Analytics, Remote Config, Cloud Messaging

### Image & Media
- **Glide 4.12.0**: Image loading and caching
- **PhotoView**: Zoomable image viewer

### Networking
- **Retrofit 2.9.0**: REST API client
- **OkHttp**: HTTP client with logging interceptor

### UI & UX
- **Material Design Components 1.12.0**: Modern Android UI
- **RecyclerView**: List and grid displays
- **ConstraintLayout**: Responsive layouts

### Monetization
- **DreamSpace Ads SDK**: Multi-network advertising platform
- **OneSignal**: Push notifications

## Important Development Notes

### Configuration Management
- Variables in UPPERCASE in `AppConfig.java` are static (not remotely configurable)
- Variables in lowercase can be updated via Firebase Remote Config
- Web API base URL is configurable: `AppConfig.general.web_url`

### Database Operations
- Use `DatabaseHandler` singleton for all database operations
- Database version is currently 4 - increment when making schema changes
- Implement proper data filtering to avoid empty/invalid entries

### Location Features
- App centers on Kota Tinggi coordinates (1.7290559, 103.8990530)
- Distance calculations support both KILOMETER and MILE metrics
- Places can be sorted by distance when GPS is enabled

### Image Handling
- Place images: `AppConfig.general.web_url + "uploads/place/" + filename`
- News images: `AppConfig.general.web_url + "uploads/news/" + filename`
- Use Glide for all image loading with proper caching

### Debugging
- Main log tag: `CITY_LOG` (defined in Constant.java)
- Enable debug mode via `BuildConfig.DEBUG`
- Firebase Analytics can be toggled via `AppConfig.general.enable_analytics`

## Common Development Patterns

### API Request Pattern
1. Define endpoint in `API.java` interface
2. Create callback model in `callbacks/` package  
3. Use `RestAdapter` to create service instance
4. Handle responses in activities/fragments

### Database Pattern
1. Add table constants in `DatabaseHandler`
2. Implement CRUD methods in `DatabaseHandler`
3. Create/update model classes in `model/` package
4. Use singleton pattern for database access

### UI Pattern
1. Create XML layout in `res/layout/`
2. Implement Activity/Fragment with proper lifecycle
3. Use adapters for RecyclerView displays
4. Follow Material Design guidelines

## Testing Notes
- No dedicated test directories found in current structure
- Manual testing recommended for location features
- Test with multiple Android versions (API 24-35)
- Verify ads integration and Firebase services

## Build Variants
- **Debug**: Development with debugging enabled
- **Release**: Production with ProGuard disabled (minifyEnabled false)
- App supports multi-dex for large dependency set