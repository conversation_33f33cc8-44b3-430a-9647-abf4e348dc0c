{"project_info": {"project_number": "341776790776", "firebase_url": "https://myreact-b20a1.firebaseio.com", "project_id": "myreact-b20a1", "storage_bucket": "myreact-b20a1.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:341776790776:android:25fc0076b199e3e1a3f70d", "android_client_info": {"package_name": "com.agroloc.malaysia"}}, "oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:341776790776:android:36eb0b757de72ab7a3f70d", "android_client_info": {"package_name": "com.app.kotatinggi"}}, "oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:341776790776:android:37d706a574a0e9b9a3f70d", "android_client_info": {"package_name": "com.melur.androidwoo"}}, "oauth_client": [{"client_id": "341776790776-qockq40rh4j9ubhkdsoohdk4u153rfmc.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.melur.androidwoo", "certificate_hash": "91c659c4053a15413f38206f95e389b47435e1c0"}}, {"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:341776790776:android:06fd8b4c973de8baa3f70d", "android_client_info": {"package_name": "com.melur.kotatinggi"}}, "oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:341776790776:android:296b25e03512b754a3f70d", "android_client_info": {"package_name": "com.ronda.malaysia"}}, "oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:341776790776:android:46941c979df410efa3f70d", "android_client_info": {"package_name": "com.sabahku.melur"}}, "oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAZyH13D0iFgSrpPiiSl5v-6wZwLOM9B5U"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "341776790776-o26quqi779a63u2og6hfvl7tlt3bq2ku.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}