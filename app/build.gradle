plugins {
    id 'com.android.application'
}

android {

    compileSdk 35
    namespace 'com.melur.kotatinggi'

    defaultConfig {
        applicationId "com.melur.kotatinggi"
        minSdk 24
        targetSdk 35
        versionCode 4
        versionName "3.1"
        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        buildConfig = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

/* IMPORTANT :
 * Be careful when update dependencies, different version library may caused error */
dependencies {
    // library for user interface
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.balysv:material-ripple:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.browser:browser:1.8.0'

    // library for api
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.6.3'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.2'
    implementation 'com.google.code.gson:gson:2.10.1'

    // library for loader image
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'

    // firebase library
    implementation platform('com.google.firebase:firebase-bom:33.5.1')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-config'

    // one signal library
    implementation 'com.onesignal:OneSignal:[5.0.0, 5.99.99]'


    // others library
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.maps.android:android-maps-utils:3.8.2'
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // AD NETWORKS OPTIONS :
    // 3.0.1-custom = Uses AdMob, Meta Audience Network, IronSource
    // 3.0.1-admob-only = Uses Admob Only
    // 3.0.1-none = Doesn't use any ads
    implementation 'com.github.dream-space:dreamspace-ads-sdk:4.0.7-all'
}

apply plugin: 'com.google.gms.google-services'

