<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="GoogleAppIndexingWarning">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.Ad_ID" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- Android 15 specific permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />

    <uses-feature android:name="android.hardware.location.gps" />

    <application
        android:name=".data.ThisApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name=".activity.ActivitySplash"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.ActivityMain"
            android:exported="false"
            android:label="@string/app_name" />
        <activity
            android:name=".activity.ActivitySetting"
            android:exported="false"
            android:label="@string/activity_title_settings"
            android:theme="@style/AppThemeSetting" />
        <activity
            android:name=".activity.ActivityMaps"
            android:exported="false"
            android:label="@string/activity_title_maps" />

        <activity
            android:name=".activity.ActivityPlaceDetail"
            android:exported="false" />
        <activity
            android:name=".activity.ActivityFullScreenImage"
            android:exported="false" />
        <activity
            android:name=".activity.ActivitySearch"
            android:exported="false" />
        <activity
            android:name=".activity.ActivityNewsInfo"
            android:exported="false" />
        <activity
            android:name=".activity.ActivityNewsInfoDetails"
            android:exported="false" />

        <!-- This meta-data tag is required to use Google Play Services. -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <!-- Add Google Map Library -->
        <uses-library android:name="com.google.android.maps" />

        <!-- One Signal Services Extension -->
        <meta-data android:name="com.onesignal.NotificationServiceExtension"
            android:value="com.app.thecity.notification.OneSignalServiceExtension" />

        <meta-data android:name="com.onesignal.NotificationOpened.DEFAULT"
            android:value="DISABLE" />

        <!-- Google API Key -->
        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="@string/GOOGLE_MAP_API_KEY" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_app_id" />

    </application>

</manifest>