<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">

    <PreferenceCategory
        android:key="@string/pref_category_notif"
        android:title="@string/pref_category_notif">

        <!-- A 'parent' preference, which enables/disables child preferences (below) when checked/unchecked. -->
        <SwitchPreference
            android:defaultValue="true"
            android:key="@string/pref_key_notif"
            android:title="@string/pref_title_notif" />

        <!-- Allows the user to choose a ringtone in the 'notification' category. -->
        <!-- NOTE: This preference will be enabled only when the checkbox above is checked. -->
        <!-- NOTE: RingtonePreference's summary should be set to its value by the activity code. -->
        <RingtonePreference
            android:defaultValue="content://settings/system/notification_sound"
            android:dependency="@string/pref_key_notif"
            android:key="@string/pref_key_ringtone"
            android:ringtoneType="notification"
            android:title="@string/pref_title_ringtone" />

        <!-- NOTE: This preference will be enabled only when the checkbox above is checked. -->
        <CheckBoxPreference
            android:defaultValue="true"
            android:dependency="@string/pref_key_notif"
            android:key="@string/pref_key_vibrate"
            android:title="@string/pref_title_vibrate" />

    </PreferenceCategory>

    <PreferenceCategory
        android:key="@string/pref_category_display"
        android:title="@string/pref_category_display">
        <Preference
            android:key="@string/pref_key_reset_cache"
            android:title="@string/pref_title_cache" />
        <Preference
            android:key="@string/pref_key_theme"
            android:title="@string/pref_title_theme" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_category_other">
        <Preference
            android:key="key_dev"
            android:summary="@string/developer_name"
            android:title="@string/pref_title_dev_name" />
        <Preference
            android:summary="@string/copyright"
            android:title="@string/pref_title_copyright" />
        <Preference
            android:key="@string/pref_title_term"
            android:title="@string/pref_title_term" />
        <Preference
            android:key="key_more"
            android:title="@string/pref_title_more" />
        <Preference
            android:key="key_rate"
            android:title="@string/pref_title_rate" />
        <Preference
            android:key="key_about"
            android:title="@string/pref_title_about" />
    </PreferenceCategory>

</PreferenceScreen>
