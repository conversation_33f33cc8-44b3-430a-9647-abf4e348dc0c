<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>

    <!--genaral spacing-->
    <dimen name="spacing_xsmall">2dp</dimen>
    <dimen name="spacing_small">3dp</dimen>
    <dimen name="spacing_medium">5dp</dimen>
    <dimen name="spacing_middle">10dp</dimen>
    <dimen name="spacing_large">15dp</dimen>
    <dimen name="spacing_mlarge">20dp</dimen>
    <dimen name="spacing_mxlarge">25dp</dimen>
    <dimen name="spacing_xlarge">35dp</dimen>
    <dimen name="spacing_xmlarge">40dp</dimen>
    <dimen name="spacing_xxlarge">50dp</dimen>

    <dimen name="elevation_card">1dp</dimen>
    <dimen name="card_corner_radius">3dp</dimen>
    <dimen name="elevation_low">9dp</dimen>
    <dimen name="elevation_high">12dp</dimen>
    <dimen name="status_bar_height">25dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!--for item recycle-->
    <dimen name="item_place_width">180dp</dimen>
    <dimen name="item_place_tag_height">45dp</dimen>

    <!--for drawer menu-->
    <dimen name="drawer_menu_width">250dp</dimen>

    <dimen name="splash_icon_size">230dp</dimen>
    <dimen name="text_desc_width">210dp</dimen>

    <!--for detail activity-->
    <dimen name="image_toolbar_height">270dp</dimen>
    <dimen name="collapse_toolbar_height">110dp</dimen>

    <dimen name="icon_loading_height">150dp</dimen>
    <dimen name="map_marker_size">40dp</dimen>

</resources>
