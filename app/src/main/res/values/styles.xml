<resources>

    <style name="AppTheme" parent="BaseTheme" />

    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item> 
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item> 
        <item name="colorControlHighlight">@color/colorPrimaryLight</item> 
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- Setting theme. -->
    <style name="AppThemeSetting" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppThemeSetting.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppThemeSetting.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="Checkbox" parent="@style/Widget.AppCompat.CompoundButton.CheckBox">
        <item name="colorPrimary">@color/colorPrimary</item> 
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item> 
        <item name="colorControlHighlight">@color/colorPrimaryLight</item> 
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="RippleStyleWhite">
        <item name="mrl_rippleOverlay">true</item>
        <item name="mrl_rippleColor">#80FFFFFF</item>
        <item name="mrl_rippleHover">true</item>
        <item name="mrl_rippleAlpha">0.2</item>
    </style>

    <style name="RippleStyleBlack" parent="RippleStyleWhite">
        <item name="mrl_rippleColor">#8096989A</item>
    </style>


</resources>
