<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- On pressed -->
    <item android:state_focused="false" android:state_pressed="true" >
        <layer-list>
            <item >
                <shape android:shape="oval" >
                    <solid android:color="@color/grey_hard" />
                    <stroke android:color="@color/grey_soft"
                        android:width="1dp"/>
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- On unpressed -->
    <item >
        <layer-list>
            <item>
                <shape android:shape="oval" >
                    <solid android:color="@color/colorAccentDark" />
                    <stroke android:color="@color/grey_soft"
                        android:width="1dp"/>
                </shape>
            </item>
        </layer-list>
    </item>

</selector>