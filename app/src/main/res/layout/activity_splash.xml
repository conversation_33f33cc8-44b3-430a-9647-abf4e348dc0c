<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingBottom="@dimen/spacing_xxlarge">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/splash_welcome_text"
            android:textAppearance="@style/TextAppearance.AppCompat.Display1"
            android:textColor="@android:color/white" />

        <ImageView
            android:layout_width="@dimen/splash_icon_size"
            android:layout_height="@dimen/splash_icon_size"
            android:layout_margin="@dimen/spacing_mlarge"
            android:src="@drawable/splash_icon_" />

        <TextView
            android:layout_width="@dimen/text_desc_width"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/splash_desc_text"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="@android:color/white" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/spacing_mxlarge" />

</RelativeLayout>
