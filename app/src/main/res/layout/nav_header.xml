<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/nav_header_lyt"
    android:layout_width="@dimen/drawer_menu_width"
    android:layout_height="wrap_content"
    android:background="@color/drawer_header_bg"
    android:fitsSystemWindows="false">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:background="@drawable/drawer_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/spacing_middle"
            android:paddingLeft="@dimen/spacing_large"
            android:paddingRight="@dimen/spacing_large"
            android:paddingTop="@dimen/spacing_middle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/menu_nav_map"
                    android:layout_width="@dimen/spacing_xmlarge"
                    android:layout_height="@dimen/spacing_xmlarge"
                    android:background="@drawable/round_bg"
                    android:clickable="true"
                    android:padding="@dimen/spacing_middle"
                    android:layout_marginRight="@dimen/spacing_large"
                    android:src="@drawable/ic_nav_map"
                    android:tint="@android:color/white" />

                <ImageView
                    android:id="@+id/menu_nav_setting"
                    android:layout_width="@dimen/spacing_xmlarge"
                    android:layout_height="@dimen/spacing_xmlarge"
                    android:background="@drawable/round_bg"
                    android:clickable="true"
                    android:padding="@dimen/spacing_middle"
                    android:src="@drawable/ic_nav_setting"
                    android:tint="@android:color/white" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_small"
                android:layout_marginTop="@dimen/spacing_xlarge"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/spacing_xxlarge"
                    android:layout_height="@dimen/spacing_xxlarge"
                    android:src="@drawable/ic_city"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/spacing_small"
                        android:text="@string/city_name"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                        android:textColor="@android:color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/city_address"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="@android:color/white" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0px"
            android:background="@color/grey_bg" />
    </LinearLayout>


</RelativeLayout>
