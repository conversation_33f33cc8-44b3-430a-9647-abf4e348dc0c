<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="1dp"
        android:clipToPadding="false"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="1dp"
        app:cardElevation="1dp"
        app:cardUseCompatPadding="false">

        <com.balysv.materialripple.MaterialRippleLayout
            style="@style/RippleStyleBlack"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/lyt_parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:minHeight="50dp"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="H,4:2"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/grey_medium">

                            <ImageView
                                android:layout_width="@dimen/icon_loading_height"
                                android:layout_height="@dimen/icon_loading_height"
                                android:layout_centerInParent="true"
                                android:src="@drawable/loading_placeholder"
                                app:tint="@color/grey_hard" />

                            <ImageView
                                android:id="@+id/image"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_alignParentStart="true"
                                android:layout_alignParentLeft="true"
                                android:layout_alignParentTop="true"
                                android:scaleType="centerCrop" />

                        </RelativeLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:layout_marginBottom="@dimen/spacing_small"
                    android:orientation="vertical"
                    android:padding="@dimen/spacing_middle">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="2"
                        android:text="News Title Placed Here"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="@color/grey_dark"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/brief_content"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:text="News Brief content will be show here"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textColor="@color/grey_hard" />


                </LinearLayout>

            </LinearLayout>

        </com.balysv.materialripple.MaterialRippleLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>