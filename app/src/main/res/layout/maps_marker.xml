<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/marker_bg"
            android:layout_width="@dimen/map_marker_size"
            android:layout_height="@dimen/map_marker_size"
            android:src="@drawable/ic_marker"
            android:tint="@color/colorPrimaryDark" />

        <ImageView
            android:id="@+id/marker_icon"
            android:layout_width="@dimen/spacing_large"
            android:layout_height="@dimen/spacing_large"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="7dp"
            android:src="@drawable/round_shape"
            android:tint="@android:color/white" />

    </RelativeLayout>
</LinearLayout>
