<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:clipToPadding="false"
    android:scrollbars="vertical"
    android:scrollingCache="true"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:descendantFocusability="blocksDescendants"
        android:orientation="vertical">

        <com.balysv.materialripple.MaterialRippleLayout
            android:id="@+id/lyt_image"
            style="@style/RippleStyleWhite"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintDimensionRatio="H,8:5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/grey_medium">

                        <ImageView
                            android:layout_width="@dimen/icon_loading_height"
                            android:layout_height="@dimen/icon_loading_height"
                            android:layout_centerInParent="true"
                            android:src="@drawable/loading_placeholder"
                            app:tint="@color/grey_hard" />

                        <ImageView
                            android:id="@+id/image"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fitsSystemWindows="true"
                            android:scaleType="centerCrop"
                            android:visibility="visible"
                            app:layout_collapseMode="parallax" />

                    </RelativeLayout>

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.balysv.materialripple.MaterialRippleLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:minHeight="@dimen/spacing_xxlarge"
            android:background="@android:color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/spacing_middle"
            android:visibility="visible">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="@dimen/spacing_large"
                android:layout_height="@dimen/spacing_large"
                android:src="@drawable/ic_date"
                app:tint="@color/grey_hard" />

            <TextView
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/spacing_middle"
                android:layout_marginLeft="@dimen/spacing_middle"
                android:text="-"
                android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                android:textColor="@color/grey_hard" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/grey_bg" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="@dimen/spacing_large">

            <WebView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/spacing_xsmall" />

        </RelativeLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>