<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:expandedTitleMarginBottom="20dp"
                app:expandedTitleMarginEnd="64dp"
                app:expandedTitleMarginStart="15dp"
                app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="H,10:7"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:background="@color/grey_medium"
                            android:layout_height="match_parent">

                            <ImageView
                                android:layout_width="@dimen/icon_loading_height"
                                android:layout_height="@dimen/icon_loading_height"
                                android:layout_centerInParent="true"
                                android:src="@drawable/loading_placeholder"
                                app:tint="@color/grey_hard" />

                            <ImageView
                                android:id="@+id/image"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:clickable="true"
                                android:scaleType="centerCrop"
                                app:layout_collapseMode="parallax" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/shape_gradient" />

                        </RelativeLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/collapse_toolbar_height"
                    android:gravity="bottom"
                    android:paddingRight="5dp"
                    app:contentInsetStartWithNavigation="0dp"
                    app:layout_collapseMode="pin"
                    app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

                <TextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="?attr/actionBarSize"
                    android:layout_gravity="bottom"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/spacing_mlarge"
                    android:text="Sample Title"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@android:color/white" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grey_bg"
            android:clipToPadding="false"
            android:scrollbars="none"
            android:scrollingCache="true"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <include layout="@layout/include_place_details_content" />

        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/lyt_progress"
            layout="@layout/include_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/spacing_mlarge"
            android:clickable="true"
            android:src="@drawable/ic_nav_favorites_outline"
            android:tint="@android:color/white"
            app:layout_anchor="@id/collapsing_toolbar"
            app:layout_anchorGravity="bottom|right|end"
            app:layout_collapseMode="parallax" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/banner_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey_bg">

        <include layout="@layout/include_ad_banner" />

    </RelativeLayout>

</LinearLayout>