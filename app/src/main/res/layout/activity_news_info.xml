<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grey_soft"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_scrollFlags="scroll|enterAlways">

                <include layout="@layout/toolbar" />

            </FrameLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/appbar"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/grey_bg"
                android:scrollbars="vertical"
                android:scrollingCache="true" />

            <include
                android:id="@+id/lyt_no_item"
                layout="@layout/include_no_item"
                android:visibility="gone" />

            <include
                android:id="@+id/lyt_progress"
                layout="@layout/include_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="@drawable/shape_gradient_soft"
            android:rotation="180"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>