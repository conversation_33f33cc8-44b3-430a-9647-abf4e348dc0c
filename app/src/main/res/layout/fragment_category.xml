<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grey_bg"
        android:clipToPadding="false"
        android:padding="3dp"
        android:scrollbars="vertical"
        android:scrollingCache="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <include
        android:id="@+id/lyt_progress"
        layout="@layout/include_progress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <include
        android:id="@+id/lyt_not_found"
        layout="@layout/include_no_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</RelativeLayout>