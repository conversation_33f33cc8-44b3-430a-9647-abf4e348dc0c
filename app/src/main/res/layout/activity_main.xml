<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/drawer_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fitsSystemWindows="true"
        tools:openDrawer="start">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:layout_scrollFlags="scroll|enterAlways">

                    <include layout="@layout/toolbar" />

                </FrameLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <FrameLayout
                android:id="@+id/frame_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/appbar"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <View
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:background="@drawable/shape_gradient_soft"
                android:rotation="180"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/spacing_large"
                android:clickable="true"
                android:src="@drawable/ic_search"
                android:tint="@android:color/white"
                app:layout_anchor="@id/frame_content"
                app:layout_anchorGravity="end|bottom" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <com.google.android.material.navigation.NavigationView
            android:id="@+id/nav_view"
            android:layout_width="@dimen/drawer_menu_width"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:background="@android:color/white"
            android:fitsSystemWindows="false"
            android:scrollbars="none"
            app:headerLayout="@layout/nav_header"
            app:itemIconTint="@color/colorAccentDark"
            app:menu="@menu/menu_drawer" />

    </androidx.drawerlayout.widget.DrawerLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey_bg">

        <include layout="@layout/include_ad_banner" />

    </RelativeLayout>

</LinearLayout>