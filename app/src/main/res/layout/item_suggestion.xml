<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lyt_parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:paddingLeft="@dimen/spacing_medium"
    android:paddingRight="@dimen/spacing_medium"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:padding="@dimen/spacing_large"
        android:src="@drawable/ic_history"
        android:tint="@color/grey_hard" />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:singleLine="true"
        android:text="Text Suggestion"
        android:fontFamily="sans-serif"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle2"
        android:textColor="@color/grey_hard" />

</LinearLayout>
