<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        android:paddingBottom="?attr/actionBarSize">

        <ImageView
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:src="@drawable/app_logo"
            android:tint="@color/grey_hard" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_item"
            android:textAppearance="@style/TextAppearance.AppCompat.Title"
            android:textColor="@color/grey_hard" />
    </LinearLayout>
</ScrollView>
