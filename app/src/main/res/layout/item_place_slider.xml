<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/spacing_middle">

    <androidx.cardview.widget.CardView
        android:id="@+id/lyt_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/spacing_middle"
        android:clipToPadding="false"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="5dp"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="false">

        <LinearLayout
            android:id="@+id/lyt_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?android:selectableItemBackground"
            android:orientation="horizontal"
            android:padding="@dimen/spacing_middle">

            <androidx.cardview.widget.CardView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_margin="@dimen/spacing_small"
                app:cardBackgroundColor="@color/grey_soft"
                app:cardCornerRadius="4dp"
                app:cardElevation="0dp">

                <ImageView
                    android:id="@+id/image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop" />

            </androidx.cardview.widget.CardView>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingHorizontal="@dimen/spacing_medium">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/spacing_medium"
                        android:text="Sample Title"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                        android:textColor="@color/grey_dark" />

                    <LinearLayout
                        android:id="@+id/lyt_distance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/spacing_large"
                            android:layout_height="@dimen/spacing_large"
                            android:layout_marginTop="3dp"
                            android:src="@drawable/ic_info_distance"
                            app:tint="@color/grey_hard" />

                        <View
                            android:layout_width="@dimen/spacing_medium"
                            android:layout_height="0dp" />

                        <TextView
                            android:id="@+id/distance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Location address"
                            android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                            android:textColor="@color/grey_hard" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lyt_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/spacing_large"
                            android:layout_height="@dimen/spacing_large"
                            android:layout_marginTop="3dp"
                            android:src="@drawable/ic_info_address"
                            app:tint="@color/grey_hard" />

                        <View
                            android:layout_width="@dimen/spacing_medium"
                            android:layout_height="0dp" />

                        <TextView
                            android:id="@+id/address"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Location address"
                            android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                            android:textColor="@color/grey_hard" />

                    </LinearLayout>

                </LinearLayout>

            </RelativeLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>

