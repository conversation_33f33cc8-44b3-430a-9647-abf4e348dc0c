<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll|enterAlways">

                <include layout="@layout/toolbar" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/spacing_large"
                    android:paddingTop="@dimen/spacing_medium"
                    android:paddingRight="@dimen/spacing_large"
                    android:paddingBottom="@dimen/spacing_large"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@android:color/white" />
            </LinearLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/appbar"
            android:background="@android:color/white"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <include
                android:id="@+id/lyt_main_content"
                layout="@layout/include_news_info_details_content" />

        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/banner_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white">

        <include layout="@layout/include_ad_banner" />

    </RelativeLayout>

</LinearLayout>