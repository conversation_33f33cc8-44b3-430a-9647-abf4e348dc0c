<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@android:color/white"
            android:paddingRight="@dimen/spacing_middle"
            android:theme="@style/ThemeOverlay.AppCompat.Light"
            app:contentInsetStartWithNavigation="0dp"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="@string/hint_search"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:singleLine="true">

                    <requestFocus />

                </EditText>

                <ImageButton
                    android:id="@+id/bt_clear"
                    android:layout_width="@dimen/spacing_xxlarge"
                    android:layout_height="@dimen/spacing_xxlarge"
                    android:background="@android:color/transparent"
                    android:src="@drawable/ic_clear"
                    android:tint="@color/grey_hard"
                    android:visibility="visible" />

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grey_bg"
            android:cacheColorHint="#0000"
            android:clipToPadding="false"
            android:padding="3dp"
            android:scrollbars="none"
            android:scrollingCache="true" />

        <include
            android:id="@+id/lyt_no_item"
            layout="@layout/include_no_item"
            android:visibility="visible" />

        <LinearLayout
            android:id="@+id/lyt_suggestion"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerSuggestion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/white"
                android:cacheColorHint="#0000"
                android:scrollbars="none"
                android:scrollingCache="true" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@color/dark_soft_overlay" />

        </LinearLayout>

    </RelativeLayout>

</LinearLayout>
