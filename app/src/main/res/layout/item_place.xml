<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="3dp"
        android:clipToPadding="false"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="2dp"
        app:cardElevation="1dp"
        app:cardUseCompatPadding="false">

        <com.balysv.materialripple.MaterialRippleLayout
            android:id="@+id/lyt_parent"
            style="@style/RippleStyleWhite"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/lyt_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/grey_medium"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="H,1:1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/grey_medium">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:src="@drawable/loading_placeholder"
                                app:tint="@color/grey_hard" />

                            <ImageView
                                android:id="@+id/image"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop" />

                        </RelativeLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_place_tag_height"
                    android:background="@color/grid_title_bg"
                    android:paddingLeft="@dimen/spacing_middle"
                    android:paddingRight="@dimen/spacing_middle">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:text="Sample Title"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle2"
                            android:textColor="@color/grey_dark" />

                        <LinearLayout
                            android:id="@+id/lyt_distance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/spacing_xsmall"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="@dimen/spacing_middle"
                                android:layout_height="@dimen/spacing_middle"
                                android:src="@drawable/ic_info_distance"
                                app:tint="@color/grey_hard" />

                            <TextView
                                android:id="@+id/distance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/spacing_small"
                                android:maxLines="1"
                                android:singleLine="true"
                                android:text="10 Km"
                                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                                android:textColor="@color/grey_hard" />

                        </LinearLayout>

                    </LinearLayout>

                </RelativeLayout>

            </LinearLayout>

        </com.balysv.materialripple.MaterialRippleLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>

