<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grey_bg"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/spacing_medium"
            android:layout_marginLeft="@dimen/spacing_middle"
            android:layout_marginRight="@dimen/spacing_medium"
            android:layout_marginTop="@dimen/spacing_large"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/spacing_medium">

                <LinearLayout
                    android:id="@+id/lyt_distance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/spacing_xxlarge"
                    android:onClick="clickLayout"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/spacing_mlarge"
                        android:layout_height="@dimen/spacing_mlarge"
                        android:layout_margin="@dimen/spacing_middle"
                        android:src="@drawable/ic_info_distance"
                        app:tint="@color/grey_hard" />

                    <TextView
                        android:id="@+id/distance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/spacing_medium"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textColor="@color/material_grey_800" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lyt_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/spacing_xxlarge"
                    android:onClick="clickLayout"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/spacing_mlarge"
                        android:layout_height="@dimen/spacing_mlarge"
                        android:layout_margin="@dimen/spacing_middle"
                        android:src="@drawable/ic_info_address"
                        app:tint="@color/grey_hard" />

                    <TextView
                        android:id="@+id/address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/spacing_medium"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textColor="@color/material_grey_800" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lyt_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/spacing_xxlarge"
                    android:onClick="clickLayout"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/spacing_mlarge"
                        android:layout_height="@dimen/spacing_mlarge"
                        android:layout_margin="@dimen/spacing_middle"
                        android:src="@drawable/ic_info_phone"
                        app:tint="@color/grey_hard" />

                    <TextView
                        android:id="@+id/phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/spacing_medium"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textColor="@color/material_grey_800" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lyt_website"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/spacing_xxlarge"
                    android:onClick="clickLayout"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/spacing_mlarge"
                        android:layout_height="@dimen/spacing_mlarge"
                        android:layout_margin="@dimen/spacing_middle"
                        android:src="@drawable/ic_info_web"
                        app:tint="@color/grey_hard" />

                    <TextView
                        android:id="@+id/website"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/spacing_medium"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textColor="@color/material_grey_800" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/spacing_medium"
            android:layout_marginLeft="@dimen/spacing_middle"
            android:layout_marginRight="@dimen/spacing_medium"
            android:layout_marginTop="@dimen/spacing_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-light"
                    android:paddingBottom="@dimen/spacing_medium"
                    android:paddingHorizontal="@dimen/spacing_large"
                    android:paddingTop="@dimen/spacing_large"
                    android:text="@string/photos_title"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@color/material_grey_800" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/galleryRecycler"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:paddingBottom="@dimen/spacing_large"
                    android:paddingLeft="@dimen/spacing_large"
                    android:paddingRight="@dimen/spacing_large" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/spacing_medium"
            android:layout_marginLeft="@dimen/spacing_medium"
            android:layout_marginRight="@dimen/spacing_middle"
            android:layout_marginTop="@dimen/spacing_large"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-light"
                    android:padding="@dimen/spacing_large"
                    android:text="Description"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@color/material_grey_800" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginBottom="@dimen/spacing_small"
                    android:layout_marginTop="@dimen/spacing_small"
                    android:background="@color/grey_bg" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingBottom="@dimen/spacing_large"
                    android:paddingLeft="@dimen/spacing_medium"
                    android:paddingRight="@dimen/spacing_medium"
                    android:paddingTop="@dimen/spacing_middle">

                    <WebView
                        android:id="@+id/description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/spacing_xsmall" />

                </RelativeLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/spacing_large"
            android:layout_marginLeft="@dimen/spacing_medium"
            android:layout_marginRight="@dimen/spacing_middle"
            android:layout_marginTop="@dimen/spacing_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card">

            <LinearLayout
                android:id="@+id/map"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-light"
                    android:padding="@dimen/spacing_large"
                    android:text="@string/map_title"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@color/material_grey_800" />

                <fragment
                    android:id="@+id/mapPlaces"
                    android:name="com.google.android.gms.maps.MapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginLeft="@dimen/spacing_large"
                    android:layout_marginRight="@dimen/spacing_large" />


                <LinearLayout
                    android:id="@+id/the_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/spacing_large"
                    android:gravity="right"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/bt_view"
                        style="?android:attr/borderlessButtonStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/map_view"
                        android:textColor="@color/colorAccent" />

                    <Button
                        android:id="@+id/bt_navigate"
                        style="?android:attr/borderlessButtonStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/map_navigate"
                        android:textColor="@color/colorAccent" />
                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</LinearLayout>
