package com.melur.kotatinggi.notification;


import android.app.Notification;
import android.content.Context;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.data.SharedPref;
import com.onesignal.notifications.IActionButton;
import com.onesignal.notifications.IDisplayableMutableNotification;
import com.onesignal.notifications.INotificationReceivedEvent;
import com.onesignal.notifications.INotificationServiceExtension;

@Keep
public class OneSignalServiceExtension implements INotificationServiceExtension {

    @Override
    public void onNotificationReceived(INotificationReceivedEvent event) {
        Context context = event.getContext();
        IDisplayableMutableNotification notification = event.getNotification();

        if (notification.getActionButtons() != null) {
            for (IActionButton button : notification.getActionButtons()) {
                // you can modify your action buttons here
            }
        }

        // this is an example of how to modify the notification by changing the background color to blue
        NotificationCompat.Extender extender = new NotificationCompat.Extender() {
            @NonNull
            @Override
            public NotificationCompat.Builder extend(@NonNull NotificationCompat.Builder builder) {
                builder.setColor(context.getResources().getColor(R.color.colorPrimary));
                builder.setSmallIcon(R.drawable.ic_notification);
                builder.setDefaults(Notification.DEFAULT_LIGHTS);
                builder.setAutoCancel(true);
                return builder;
            }
        };
        notification.setExtender(extender);

        //If you need to perform an async action or stop the payload from being shown automatically,
        //use event.preventDefault(). Using event.notification.display() will show this message again.
        if(!new SharedPref(context).getNotification()){
            event.preventDefault();
        }
    }
}