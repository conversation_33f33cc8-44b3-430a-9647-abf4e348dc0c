package com.melur.kotatinggi.notification;

import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

import android.content.Context;
import android.content.Intent;

import com.melur.kotatinggi.AppConfig;
import com.melur.kotatinggi.BuildConfig;
import com.melur.kotatinggi.R;
import com.melur.kotatinggi.activity.ActivityMain;
import com.melur.kotatinggi.activity.ActivityNewsInfoDetails;
import com.melur.kotatinggi.activity.ActivityPlaceDetail;
import com.melur.kotatinggi.activity.ActivitySplash;
import com.melur.kotatinggi.data.DatabaseHandler;
import com.melur.kotatinggi.model.NewsInfo;
import com.melur.kotatinggi.model.NotificationData;
import com.melur.kotatinggi.model.Place;
import com.google.gson.Gson;
import com.onesignal.Continue;
import com.onesignal.OneSignal;
import com.onesignal.debug.LogLevel;

import org.json.JSONException;
import org.json.JSONObject;

public class NotificationHelper {

    public static NotificationData notificationData = null;

    public static void init(Context context) {
        oneSignalInit(context);
    }

    public static void oneSignalInit(Context context) {
        if (BuildConfig.DEBUG) {
            OneSignal.getDebug().setLogLevel(LogLevel.VERBOSE);
        }

        // init one signal with client data
        OneSignal.initWithContext(context, AppConfig.notification.notif_one_signal_appid);
        OneSignal.getUser().addTag("APP", context.getResources().getString(R.string.app_name));

        // handle when use click notification
        onOneSignalOpenNotification(context);
    }

    public static void requestNotificationPermission(Context context) {
        // requestPermission will show the native Android notification permission prompt.
        // NOTE: It's recommended to use a OneSignal In-App Message to prompt instead.
        OneSignal.getNotifications().requestPermission(false, Continue.none());
    }

    public static String getOnesignalId(Context context) {
        return OneSignal.getUser().getPushSubscription().getId();
    }

    public static void onOneSignalOpenNotification(Context context) {
        OneSignal.getNotifications().addClickListener(iNotificationClickEvent -> {
            notificationData = new NotificationData();
            JSONObject jsonObject = iNotificationClickEvent.getNotification().getAdditionalData();
            if(jsonObject != null){
                try {
                    String place_str;
                    place_str = jsonObject.getString("place");
                    notificationData.place = !place_str.isEmpty() ? new Gson().fromJson(place_str, Place.class) : null;
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }

                try {
                    String news_str;
                    news_str = jsonObject.getString("news");
                    notificationData.news = !news_str.isEmpty() ? new Gson().fromJson(news_str, NewsInfo.class) : null;
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }

                if(notificationData.place == null && notificationData.news == null){
                    notificationData = null;
                }

                if(ActivityMain.active){
                    checkNotificationData(context);
                } else {
                    Intent intent = new Intent(context, ActivitySplash.class);
                    intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);
                }
            }
        });

    }

    public static void checkNotificationData(Context context){
        if(notificationData == null) return;
        Intent intent = new Intent(context, ActivitySplash.class);
        if (notificationData.place != null) {
            intent = ActivityPlaceDetail.navigateBase(context, notificationData.place, true);
        } else if (notificationData.news != null) {
            new DatabaseHandler(context).refreshTableNewsInfo();
            intent = ActivityNewsInfoDetails.navigateBase(context, notificationData.news, true);
        }
        NotificationHelper.notificationData = null;
        intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }


}