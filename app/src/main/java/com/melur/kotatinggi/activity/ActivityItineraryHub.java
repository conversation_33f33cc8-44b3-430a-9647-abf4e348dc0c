package com.melur.kotatinggi.activity;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.melur.kotatinggi.R;
import com.melur.kotatinggi.utils.Tools;

public class ActivityItineraryHub extends AppCompatActivity {

    private TextView tvMessage;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_itinerary_hub);
        
        initToolbar();
        initComponents();
    }

    private void initToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setDisplayShowTitleEnabled(true);
            actionBar.setTitle("My Itineraries");
        }
        Tools.systemBarLolipop(this);
    }

    private void initComponents() {
        tvMessage = findViewById(R.id.tvMessage);
        tvMessage.setText("Itinerary feature is coming soon!\n\nThis feature will allow you to:\n• Plan your trips\n• Track visited places\n• Create custom itineraries\n• Share your travel plans");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
