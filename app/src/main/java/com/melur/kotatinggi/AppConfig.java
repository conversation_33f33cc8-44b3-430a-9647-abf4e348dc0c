package com.melur.kotatinggi;

import com.melur.kotatinggi.utils.AppConfigExt;

import java.io.Serializable;

import dreamspace.ads.sdk.data.AdNetworkType;

public class AppConfig extends AppConfigExt implements Serializable {

    /* -------------------------------------- INSTRUCTION : ----------------------------------------
     * This is config file used for this app, you can configure Ads, Notification, and General data from this file
     * some values are not explained and can be understood easily according to the variable name
     * value can change remotely (optional), please read documentation to follow instruction
     *
     * variable with UPPERCASE name will NOT fetch / replace with remote config
     * variable with LOWERCASE name will fetch / replace with remote config
     * See video Remote Config tutorial https://www.youtube.com/watch?v=tOKXwOTqOzA
     ----------------------------------------------------------------------------------------------*/

    /* set true for fetch config with firebase remote config, */
    public static final boolean USE_REMOTE_CONFIG = true;

    /* force rtl layout direction */
    public static final boolean RTL_LAYOUT = false;

    /* config for General Application */
    public static class General implements Serializable {

        /* Edit WEB_URL with your url. Make sure you have backslash('/') in the end url */
        public String web_url = "https://kotatinggi.herbagus.net/";

        /* for map default zoom */
        public double city_lat = 1.7290559;
        public double city_lng = 103.8990530;

        /* this flag if you want to hide menu news info */
        public boolean enable_news_info = true;

        /* if you place data more than 200 items please set TRUE */
        public boolean lazy_load = false;

        /* flag for tracking analytics */
        public boolean enable_analytics = true;

        /* clear image cache when receive push notifications */
        public boolean refresh_img_notif = true;

        /* when user enable gps, places will sort by distance */
        public boolean sort_by_distance = true;

        /* distance metric, fill with KILOMETER or MILE only */
        public String distance_metric_code = "KILOMETER";

        /* related to UI display string */
        public String distance_metric_str = "Km";

        /* flag for enable disable theme color chooser, in Setting */
        public boolean theme_color = true;

        /* true for open link in internal app browser, not external app browser */
        public boolean open_link_in_app = true;

        /* this limit value used for give pagination (request and display) to decrease payload */
        public int limit_place_request = 40;
        public int limit_loadmore = 40;
        public int limit_news_request = 40;

        /* 2 links below will use on setting page */
        public String more_apps_url = "https://play.google.com/store/apps/developer?id=M-D-C";
        public String contact_us_url = "mailto:<EMAIL>";
    }

    /* One Signal Notification */
    public static class Notification {
        public String notif_one_signal_appid = "ff781e34-3be7-40b1-b023-4858b6422256";
    }

    /* config for Ad Network */
    public static class Ads implements Serializable {

        /* enable disable ads */
        public boolean ad_enable = true;

        /* MULTI Ad network selection,
         * Fill this array to enable ad backup flow, left this empty to use single ad_network above
         * app will try show sequentially from this array
         * example flow ADMOB > FAN > IRONSOURCE
         *
         * OPTION :
         * ADMOB,MANAGER, FAN,IRONSOURCE, FAN_BIDDING_ADMOB, FAN_BIDDING_AD_MANAGER, FAN_BIDDING_IRONSOURCE
         * */
        public AdNetworkType[] ad_networks = {
                AdNetworkType.ADMOB,
                AdNetworkType.FAN,
                AdNetworkType.IRONSOURCE,
        };

        public boolean ad_enable_gdpr = true;

        /* disable enable ads each page */
        public boolean ad_main_banner = true;
        public boolean ad_main_interstitial = true;
        public boolean ad_place_details_banner = true;
        public boolean ad_news_details_banner = true;

        /* show interstitial after several action, this value for action counter */
        public int ad_inters_interval = 7;

        /* ad unit for ADMOB */
        public String ad_admob_publisher_id = "pub-5634188469728743";
        public String ad_admob_banner_unit_id = "ca-app-pub-5634188469728743/6169057910";
        public String ad_admob_interstitial_unit_id = "ca-app-pub-5634188469728743/2640876447";
        public String ad_admob_rewarded_unit_id = "ca-app-pub-5634188469728743/7087153760";
        public String ad_admob_open_app_unit_id = "ca-app-pub-5634188469728743/6129295313";

        /* ad unit for Google Ad Manager */
        public String ad_manager_banner_unit_id = "/6499/example/banner";
        public String ad_manager_interstitial_unit_id = "/6499/example/interstitial";
        public String ad_manager_rewarded_unit_id = "/6499/example/rewarded";
        public String ad_manager_open_app_unit_id = "/6499/example/app-open";

        /* ad unit for FAN */
        public String ad_fan_banner_unit_id = "YOUR_PLACEMENT_ID";
        public String ad_fan_interstitial_unit_id = "YOUR_PLACEMENT_ID";
        public String ad_fan_rewarded_unit_id = "YOUR_PLACEMENT_ID";

        /* ad unit for IRON SOURCE */
        public String ad_ironsource_app_key = "170112cfd";
        public String ad_ironsource_banner_unit_id = "DefaultBanner";
        public String ad_ironsource_rewarded_unit_id = "DefaultRewardedVideo";
        public String ad_ironsource_interstitial_unit_id = "DefaultInterstitial";
    }

}
