package com.melur.kotatinggi.data;

import android.app.Application;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.melur.kotatinggi.AppConfig;
import com.melur.kotatinggi.connection.API;
import com.melur.kotatinggi.connection.RestAdapter;
import com.melur.kotatinggi.connection.callbacks.CallbackDevice;
import com.melur.kotatinggi.model.DeviceInfo;
import com.melur.kotatinggi.notification.NotificationHelper;
import com.melur.kotatinggi.utils.Tools;
import com.google.firebase.FirebaseApp;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;

import retrofit2.Call;
import retrofit2.Response;

public class ThisApplication extends Application {

    private Call<CallbackDevice> callback = null;
    private static ThisApplication mInstance;
    private FirebaseAnalytics firebaseAnalytics;
    private FirebaseRemoteConfig firebaseRemoteConfig;
    private Location location = null;
    private SharedPref sharedPref;
    private final int FCM_MAX_COUNT = 5;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(Constant.LOG_TAG, "onCreate : ThisApplication");
        mInstance = this;
        sharedPref = new SharedPref(this);

        NotificationHelper.oneSignalInit(this);
        initFirebase();
        initRemoteConfig();
        initFirebaseAnalytics();

        // obtain regId & registering device to server
        obtainNotificationToken(0);
    }

    public static synchronized ThisApplication getInstance() {
        return mInstance;
    }


    private void obtainNotificationToken(int retryCount) {
        Log.d("NOTIF_TOKEN", "getOnesignalId | " + retryCount);
        String token = "";
        try {
            token = NotificationHelper.getOnesignalId(this);
        } catch (Exception ignored) {
        }

        if(token.isEmpty()){
            if (retryCount > FCM_MAX_COUNT) return;
            new Handler(this.getMainLooper()).postDelayed(() -> obtainNotificationToken(retryCount + 1), 500);
        } else {
            sharedPref.setFcmRegId(token);
            if (!TextUtils.isEmpty(token)) sendRegistrationToServer(token);
        }
    }

    /**
     * --------------------------------------------------------------------------------------------
     * For Firebase Cloud Messaging
     */
    private void sendRegistrationToServer(String token) {
        if (Tools.cekConnection(this)) {
            API api = RestAdapter.createAPI();
            DeviceInfo deviceInfo = Tools.getDeviceInfo(this);
            deviceInfo.setRegid(token);

            callback = api.registerDevice(deviceInfo);
            callback.enqueue(new retrofit2.Callback<CallbackDevice>() {
                @Override
                public void onResponse(Call<CallbackDevice> call, Response<CallbackDevice> response) {
                    CallbackDevice resp = response.body();
                }

                @Override
                public void onFailure(Call<CallbackDevice> call, Throwable t) {
                }
            });
        }
    }

    private void initFirebase() {
        // Obtain the Firebase Analytics.
        FirebaseApp.initializeApp(this);
        FirebaseAnalytics.getInstance(this);
    }

    /* For Remote Config*/
    private void initRemoteConfig() {
        FirebaseApp.initializeApp(this);
        firebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
        if (!AppConfig.USE_REMOTE_CONFIG) return;
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(60)
                .setFetchTimeoutInSeconds(3)
                .build();
        firebaseRemoteConfig.setConfigSettingsAsync(configSettings);
    }

    /* For Google Analytics */
    public synchronized FirebaseAnalytics initFirebaseAnalytics() {
        if (firebaseAnalytics == null && AppConfig.general.enable_analytics) {
            // Obtain the Firebase Analytics.
            firebaseAnalytics = FirebaseAnalytics.getInstance(this);
        }
        return firebaseAnalytics;
    }

    public void trackScreenView(String event) {
        if (firebaseAnalytics == null || !AppConfig.general.enable_analytics) return;
        Bundle params = new Bundle();
        event = event.replaceAll("[^A-Za-z0-9_]", "");
        params.putString("event", event);
        firebaseAnalytics.logEvent(event, params);
    }

    public void trackEvent(String category, String action, String label) {
        if (firebaseAnalytics == null || !AppConfig.general.enable_analytics) return;
        Bundle params = new Bundle();
        category = category.replaceAll("[^A-Za-z0-9_]", "");
        action = action.replaceAll("[^A-Za-z0-9_]", "");
        label = label.replaceAll("[^A-Za-z0-9_]", "");
        params.putString("category", category);
        params.putString("action", action);
        params.putString("label", label);
        firebaseAnalytics.logEvent("EVENT", params);
    }

    public FirebaseRemoteConfig getFirebaseRemoteConfig() {
        return firebaseRemoteConfig;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }
}
