# KotaTinggi - Tourism & Location Discovery App

[![Android](https://img.shields.io/badge/Platform-Android-green.svg)](https://android.com)
[![API](https://img.shields.io/badge/API-24%2B-brightgreen.svg?style=flat)](https://android-arsenal.com/api?level=24)
[![Target SDK](https://img.shields.io/badge/Target%20SDK-35-blue.svg)](https://developer.android.com/about/versions/15)
[![Version](https://img.shields.io/badge/Version-2.0-orange.svg)](https://github.com/terranoss/KotaTinggi)

KotaTinggi is a comprehensive Android application designed to help users discover and explore tourist destinations, local attractions, and points of interest. The app provides detailed information about places, interactive maps, and location-based services to enhance the travel and exploration experience.

## 🌟 Features

### Core Functionality
- **Location Discovery**: Browse and discover tourist attractions, restaurants, hotels, and local points of interest
- **Interactive Maps**: Google Maps integration with custom markers and location details
- **Category-based Browsing**: Organized content by categories (Tourism, Food, Accommodation, etc.)
- **Search Functionality**: Advanced search with filters for finding specific places
- **Favorites System**: Save and manage favorite locations for quick access
- **Detailed Place Information**: Comprehensive details including descriptions, contact info, and images

### User Experience
- **Grid & List Views**: Multiple viewing options for place listings
- **Image Gallery**: High-quality images with zoom and pan capabilities
- **Location Services**: GPS integration for distance calculation and navigation
- **Offline Support**: Local database storage for offline browsing
- **Push Notifications**: Stay updated with new places and announcements

### Technical Features
- **Real-time Data Sync**: Firebase integration for dynamic content updates
- **Performance Optimized**: Efficient image loading and caching
- **Material Design**: Modern Android UI/UX following Material Design guidelines
- **Multi-language Support**: Configurable language settings
- **Analytics Integration**: Firebase Analytics for usage insights

## 📱 Screenshots

| Home Screen | Place Details | Map View | Categories |
|-------------|---------------|----------|------------|
| ![Home](screenshots/home.png) | ![Details](screenshots/details.png) | ![Map](screenshots/map.png) | ![Categories](screenshots/categories.png) |

*Note: Screenshots showcase the main features including place discovery, detailed information, interactive maps, and category browsing*

## 🛠️ Technical Specifications

### Development Environment
- **Language**: Java
- **Platform**: Android (API 24+)
- **Target SDK**: Android 15 (API 35)
- **IDE**: Android Studio
- **Build System**: Gradle

### Architecture & Libraries
- **Architecture**: MVC (Model-View-Controller) with Repository pattern
- **Database**: SQLite with custom DatabaseHandler and data validation
- **Maps**: Google Maps Android API with custom markers and clustering
- **Image Loading**: Glide for efficient image handling and caching
- **Networking**: Volley for API communications with retry mechanisms
- **Backend**: Firebase (Analytics, Messaging, Remote Config, Crashlytics)
- **UI Components**: Material Design Components with custom styling
- **Location Services**: Google Play Services Location with background updates
- **Background Processing**: WorkManager for scheduled tasks
- **Notifications**: OneSignal integration for push notifications

### Design Patterns Used
- **Repository Pattern**: Centralized data access layer
- **Observer Pattern**: UI updates based on data changes
- **Singleton Pattern**: Database and configuration management
- **Factory Pattern**: Object creation for different place types
- **Adapter Pattern**: RecyclerView adapters for different layouts
- **Builder Pattern**: Complex object construction (e.g., map markers)

### Key Dependencies
```gradle
// Google Services & Maps
implementation 'com.google.android.gms:play-services-maps:19.0.0'
implementation 'com.google.android.gms:play-services-location:21.3.0'
implementation 'com.google.maps.android:android-maps-utils:3.8.2'

// Firebase
implementation platform('com.google.firebase:firebase-bom:33.5.1')
implementation 'com.google.firebase:firebase-analytics'
implementation 'com.google.firebase:firebase-config'

// Image Loading & UI
implementation 'com.github.bumptech.glide:glide:4.16.0'
implementation 'com.github.chrisbanes:PhotoView:2.3.0'
implementation 'com.google.android.material:material:1.12.0'

// Networking & Utilities
implementation 'com.android.volley:volley:1.2.1'
implementation 'androidx.work:work-runtime:2.9.1'
```

## 🏗️ Project Structure

```
app/
├── src/main/
│   ├── java/com/melur/kotatinggi/
│   │   ├── activity/          # Activity classes
│   │   ├── adapter/           # RecyclerView adapters
│   │   ├── data/              # Database and data models
│   │   ├── fragment/          # Fragment classes
│   │   ├── model/             # Data model classes
│   │   ├── utils/             # Utility classes
│   │   └── widget/            # Custom UI components
│   ├── res/
│   │   ├── layout/            # XML layout files
│   │   ├── values/            # Strings, colors, styles
│   │   ├── drawable/          # Images and vector drawables
│   │   ├── mipmap/            # App icons
│   │   └── xml/               # Backup rules and configurations
│   └── AndroidManifest.xml
├── build.gradle               # Module-level build configuration
└── google-services.json       # Firebase configuration
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK with API level 24 or higher
- Google Play Services
- Firebase project setup (optional for full functionality)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/terranoss/KotaTinggi.git
   cd KotaTinggi
   ```

2. **Open in Android Studio**
   - Launch Android Studio
   - Select "Open an existing Android Studio project"
   - Navigate to the cloned directory and select it

3. **Configure Firebase (Optional)**
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Add your Android app to the Firebase project
   - Download `google-services.json` and place it in the `app/` directory
   - Enable Analytics, Remote Config, and Cloud Messaging as needed

4. **Set up Google Maps API**
   - Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com)
   - Add the API key to your `local.properties` file:
     ```
     MAPS_API_KEY=your_api_key_here
     ```

5. **Build and Run**
   ```bash
   ./gradlew assembleDebug
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

## 🔧 API Integration

### Google Maps API Setup
1. **Enable APIs** in Google Cloud Console:
   - Maps SDK for Android
   - Places API
   - Geocoding API
   - Directions API

2. **Configure API Key** in `app/src/main/res/values/google_maps_api.xml`:
   ```xml
   <resources>
       <string name="google_maps_key" templateMergeStrategy="preserve" translatable="false">YOUR_API_KEY</string>
   </resources>
   ```

### Firebase Configuration
1. **Required Services**:
   - Firebase Analytics
   - Firebase Remote Config
   - Firebase Cloud Messaging
   - Firebase Crashlytics (optional)

2. **Configuration Files**:
   - Place `google-services.json` in `app/` directory
   - Ensure package name matches your Firebase project

## 🐛 Troubleshooting

### Common Issues

#### Build Issues
**Problem**: `google-services.json` not found
```
Solution: Download the file from Firebase Console and place it in app/ directory
```

**Problem**: Maps not displaying
```
Solution:
1. Check API key configuration
2. Ensure Maps SDK is enabled in Google Cloud Console
3. Verify package name and SHA-1 fingerprint
```

#### Runtime Issues
**Problem**: Location not working
```
Solution:
1. Grant location permissions in device settings
2. Enable GPS/Location services
3. Check network connectivity
```

**Problem**: Empty places showing
```
Solution: The app now automatically filters empty places. If issues persist:
1. Clear app data
2. Force refresh from settings
3. Check network connection for data sync
```

### Debug Mode
Enable debug logging by setting `BuildConfig.DEBUG` flag:
```java
if (BuildConfig.DEBUG) {
    Log.d("KotaTinggi", "Debug information");
}
```

## 📊 Database Schema

The app uses SQLite database with the following main tables:

### Places Table
- `place_id` (INTEGER PRIMARY KEY)
- `name` (TEXT) - Place name
- `image` (TEXT) - Main image URL
- `address` (TEXT) - Physical address
- `phone` (TEXT) - Contact number
- `website` (TEXT) - Website URL
- `description` (TEXT) - Detailed description
- `lat`, `lng` (REAL) - GPS coordinates
- `distance` (REAL) - Distance from user
- `last_update` (INTEGER) - Timestamp

### Categories Table
- `cat_id` (INTEGER PRIMARY KEY)
- `name` (TEXT) - Category name
- `icon` (TEXT) - Category icon
- `color` (TEXT) - Category color
- `brief` (TEXT) - Brief description

### Additional Tables
- `place_category` - Many-to-many relationship between places and categories
- `images` - Additional images for places
- `favorites` - User's favorite places

## 🔧 Configuration

### App Configuration
The app supports various configuration options through Firebase Remote Config:

- **Refresh intervals**: Control data sync frequency
- **Feature flags**: Enable/disable specific features
- **Content filters**: Customize content display
- **API endpoints**: Configure backend services

### Build Variants
- **Debug**: Development build with debugging enabled
- **Release**: Production build with optimizations and obfuscation

## 🧪 Testing

### Running Tests
```bash
# Unit tests
./gradlew test

# Instrumented tests
./gradlew connectedAndroidTest

# Generate test reports
./gradlew jacocoTestReport
```

### Test Coverage
The project includes:
- **Unit Tests**: Data models, utilities, and business logic
- **Integration Tests**: Database operations and API interactions
- **UI Tests**: Critical user flows and navigation
- **Performance Tests**: Memory usage and response time validation

### Testing Strategy
- **Automated Testing**: CI/CD pipeline with automated test execution
- **Manual Testing**: Device compatibility and user experience validation
- **Regression Testing**: Ensuring new features don't break existing functionality

## 📈 Performance Optimizations

### Implemented Optimizations
- **Image Caching**: Glide-based image loading with memory and disk caching
- **Database Optimization**: Indexed queries and efficient data filtering
- **Memory Management**: Proper lifecycle management and memory leak prevention
- **Network Optimization**: Request batching and caching strategies
- **UI Performance**: RecyclerView optimization and smooth scrolling

### Data Filtering
Recent improvements include comprehensive data filtering to ensure only valid places are displayed:
- Empty place validation at database level
- UI-level filtering for additional safety
- Automatic cleanup of invalid data entries

## 🔒 Privacy & Security

### Data Protection
- **Backup Rules**: Configured data extraction and backup policies for Android 15
- **Permission Management**: Minimal required permissions with runtime requests
- **Data Encryption**: Sensitive data protection in local storage
- **Privacy Compliance**: GDPR and privacy regulation compliance

### Permissions Required
- `ACCESS_FINE_LOCATION` - For location-based features
- `ACCESS_COARSE_LOCATION` - For general location services
- `INTERNET` - For data synchronization
- `ACCESS_NETWORK_STATE` - For network status monitoring
- `POST_NOTIFICATIONS` - For push notifications (Android 13+)

## 🚀 Deployment

### Google Play Store
The app is configured for Google Play Store deployment with:
- Target SDK 35 (Android 15) for compliance
- Proper signing configuration
- Optimized APK/AAB generation
- Play Console integration ready

### Release Process
1. Update version code and name in `build.gradle`
2. Generate signed release build
3. Test on multiple devices and Android versions
4. Upload to Google Play Console
5. Configure store listing and metadata

## 🤝 Contributing

We welcome contributions to improve KotaTinggi! Please follow these guidelines:

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Code Style
- Follow Android coding conventions
- Use meaningful variable and method names
- Add comments for complex logic
- Ensure proper error handling

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Developer**: Deen (terranoss)
- **Project Type**: Tourism & Location Discovery App
- **Platform**: Android

## 📞 Support

For support, questions, or feedback:
- **GitHub Issues**: [Create an issue](https://github.com/terranoss/KotaTinggi/issues) for bug reports or feature requests
- **Documentation**: Check this README and inline code documentation
- **Community**: Join discussions in the project's GitHub Discussions
- **Email**: Contact the development team for urgent matters

## 🛣️ Roadmap

### Upcoming Features
- [ ] **Social Features**: User reviews and ratings for places
- [ ] **Offline Maps**: Download maps for offline usage
- [ ] **AR Integration**: Augmented reality for place discovery
- [ ] **Multi-language**: Support for multiple languages
- [ ] **Dark Theme**: Complete dark mode implementation
- [ ] **Voice Search**: Voice-activated search functionality
- [ ] **Trip Planning**: Create and manage travel itineraries

### Technical Improvements
- [ ] **Jetpack Compose**: Migrate to modern UI toolkit
- [ ] **Room Database**: Replace SQLite with Room persistence library
- [ ] **Kotlin Migration**: Gradual migration from Java to Kotlin
- [ ] **Architecture Components**: Implement MVVM with LiveData
- [ ] **Dependency Injection**: Add Dagger/Hilt for better architecture

## 🏆 Achievements & Recognition

- ✅ **Google Play Ready**: Compliant with latest Android requirements
- ✅ **Performance Optimized**: Smooth user experience across devices
- ✅ **Security Focused**: Implements Android security best practices
- ✅ **Accessibility**: Supports accessibility features for inclusive design

## 🔄 Version History

### Version 2.0 (Current)
- Updated to target Android 15 (API 35)
- Enhanced data filtering and validation
- Improved performance and stability
- Updated dependencies and security features
- Added comprehensive backup and privacy policies

### Version 1.0
- Initial release
- Core location discovery features
- Google Maps integration
- Basic search and favorites functionality

## 🔄 Data Flow Architecture

```
User Interface (Activities/Fragments)
           ↓
    Adapters & ViewHolders
           ↓
    DatabaseHandler (Repository)
           ↓
    SQLite Database ← → Firebase Remote Config
           ↓
    Data Models (Place, Category, etc.)
           ↓
    Google Maps API & Location Services
```

### Data Synchronization
1. **Local First**: App works offline with local SQLite database
2. **Background Sync**: Periodic updates from remote sources
3. **Conflict Resolution**: Last-write-wins strategy for data conflicts
4. **Cache Management**: Intelligent caching for images and API responses

## 📊 Analytics & Monitoring

### Implemented Tracking
- **User Engagement**: Screen views, button clicks, search queries
- **Performance Metrics**: App startup time, API response times
- **Error Tracking**: Crash reports and error analytics
- **Location Analytics**: Popular places and user movement patterns

### Privacy Compliance
- **Data Anonymization**: Personal data is anonymized in analytics
- **Opt-out Options**: Users can disable analytics in settings
- **GDPR Compliance**: Proper consent management for EU users

---

## 📋 Quick Start Checklist

- [ ] Clone the repository
- [ ] Set up Android Studio with SDK 35
- [ ] Configure Google Maps API key
- [ ] Set up Firebase project (optional)
- [ ] Build and run the debug version
- [ ] Test core features (maps, search, favorites)
- [ ] Review code structure and documentation

---

<div align="center">

**KotaTinggi** - Discover Amazing Places Around You

[![GitHub stars](https://img.shields.io/github/stars/terranoss/KotaTinggi?style=social)](https://github.com/terranoss/KotaTinggi/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/terranoss/KotaTinggi?style=social)](https://github.com/terranoss/KotaTinggi/network)
[![GitHub issues](https://img.shields.io/github/issues/terranoss/KotaTinggi)](https://github.com/terranoss/KotaTinggi/issues)

Made with ❤️ for tourism and exploration enthusiasts

*Empowering travelers to discover hidden gems and create memorable experiences*

</div>
